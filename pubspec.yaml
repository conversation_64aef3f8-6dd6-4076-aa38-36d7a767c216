name: transport_match
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cached_network_image: ^3.4.1
  cupertino_icons: ^1.0.8
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  flash: ^3.1.1
  flutter:
    sdk: flutter
  flutter_kronos:
    path: packages/flutter-kronos_0.1.0+1
  # flutter_local_notifications: ^17.2.3
  flutter_localizations:
    sdk: flutter
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.10+1
  fluttertoast: ^8.2.8
  form_field_validator: ^1.1.0
  geolocator: ^13.0.2
  get_it: ^7.7.0
  google_maps_flutter: ^2.10.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  image_picker: ^1.1.2
  intl: any
  logger: ^2.4.0
  package_info_plus: ^8.0.2
  path: ^1.9.0
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  pinput: ^5.0.0
  pkg_dio:
    path: packages/pkg_dio
  provider: ^6.1.2
  # shared_preferences: ^2.3.2
  shimmer: ^3.0.0
  geocoding: ^3.0.0
  dropdown_textfield: 
    path: packages/dropdown_textfield_1.2.0
  flutter_typeahead: ^5.2.0
  gap: ^3.0.1
  # pull_to_refresh: ^2.0.0
  connectivity_plus: ^6.1.3
  http: ^1.3.0
  webview_flutter: ^4.10.0
#  widget_zoom: ^0.0.4
  flutter_rating: ^2.0.2
  readmore: ^3.0.0
  url_launcher: ^6.3.1
  custom_info_window: ^1.0.1
  socket_io_client: ^3.1.2
  country_picker: ^2.0.27
  firebase_notifications_handler: ^2.0.2
  flutter_udid: ^4.0.0
  lottie: ^3.3.1
  go_router: ^11.0.0
  envied: any
  eraser: ^3.0.0
  easy_refresh: ^3.4.0
  
  pull_to_refresh: any
dev_dependencies:
  build_runner: ^2.4.12
  change_app_package_name: ^1.4.0
  envied_generator: ^0.5.4+1
  flutter_gen_runner: ^5.5.0+1
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.3
  flutter_test:
    sdk: flutter
  very_good_analysis: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animation_loader/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,

  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 400
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-ExtraBold.ttf
          weight: 900

    # - family: Trajan Pro
    #   fonts:
    #     - asset: fonts/TrajanPro.ttf
    #     - asset: fonts/TrajanPro_Bold.ttf
    #       weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_gen:
  output: lib/utils/gen/ # Optional (default: lib/gen/)
  line_length: 120 # Optional (default: 80)

  integrations:
    flutter_svg: true
    lottie: true

  assets:
    enabled: true

    outputs:
      class_name: AppAssets
      package_parameter_enabled: false
      # Assets.imagesChip
      style: camel-case

      # Assets.images_chip
      # style: snake-case

      # Assets.images.chip (default style)
      # style: dot-delimiter

  fonts:
    enabled: true
    outputs:
      class_name: AppFontFamily

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/images/logo.png
  android: true
  ios: true
  android_12:
    color: "#FFFFFF"
    image: assets/images/logo.png
