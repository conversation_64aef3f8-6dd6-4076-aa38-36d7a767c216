import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/model/payment_param.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/payment_screen.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/shipment_confirmation_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Booking module routes configuration.
class BookingRoutes {
  static List<GoRoute> buildBookingRoutes() {
    return [
      GoRoute(
        path: AppRoutes.bookingShipmentConfirmationPath,
        name: AppRoutes.bookingShipmentConfirmationScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ShipmentConfirmationParams;
          return ShipmentConfirmationScreen(
            shipmentConfirmationParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.bookingPaymentPath,
        name: AppRoutes.bookingPaymentScreen,
        parentNavigatorKey: rootNav<PERSON><PERSON>,
        builder: (context, state) {
          final params = state.extra! as PaymentParam;
          return PaymentScreen(
            isExclusiveTrip: params.isExclusiveTrip,
            bookingId: params.bookingId,
            bookingProviderData: params.bookingProviderData,
            paymentDataModel: params.paymentDataModel,
          );
        },
      ),
    ];
  }
}
