import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/marqee_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

/// Transporter card
class TransporterCard extends StatelessWidget {
  /// Constructor
  const TransporterCard({
    super.key,
    required this.data,
    required this.isSelected,
    this.totalSlot,
    this.isTrip = false,
    this.isBorder = false,
    this.isDisabled = (false, ''),
    // this.tripData,
  });
  final ProviderListData data;
  final bool isSelected;
  final bool isTrip;
  final bool isBorder;
  final num? totalSlot;
  final (bool, String) isDisabled;
  // final TripModel? tripData;

  @override
  Widget build(BuildContext context) {
    final finalSlot =
        ((totalSlot != null && totalSlot != 0)
                ? (data.availableSlot ?? 0) >= (totalSlot ?? 0)
                      ? totalSlot
                      : data.availableSlot
                : data.availableSlot)
            ?.round();
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppSize.r4),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.all(isBorder ? AppSize.h12 : AppSize.h16),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.fff2eef8 : Colors.white,
                border: isBorder ? Border.all(color: AppColors.ffADB5BD) : null,
                borderRadius: BorderRadius.circular(AppSize.r4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// Header row with "Your Shipment" and "Edit"
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          data.companyName ?? '',
                          style: context.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.normal,
                            overflow: TextOverflow.ellipsis,
                            color: AppColors.ff343A40,
                          ),
                        ),
                      ),
                      Text.rich(
                        TextSpan(
                          text:
                              '${data.totalCost?.toStringAsFixed(1).smartFormat()}',
                          children: [
                            TextSpan(
                              text: '/car',
                              style: context.textTheme.titleLarge?.copyWith(
                                color: AppColors.ff67509C,
                                fontSize: AppSize.sp10,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: AppColors.ff67509C,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Gap(AppSize.h4),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.ffFFC107,
                        size: AppSize.h18,
                      ),
                      Gap(AppSize.w4),
                      Text(
                        data.ratings?.toStringAsFixed(1) ?? '0.0',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.ff6C757D,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (isSelected)
                        Padding(
                          padding: EdgeInsets.only(
                            right: AppSize.w2,
                            left: AppSize.w16,
                          ),
                          child: Icon(
                            Icons.check_rounded,
                            color: AppColors.ff67509C,
                            size: AppSize.w14,
                          ),
                        ),
                      if (isSelected)
                        Text(
                          context.l10n.assigned,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: AppColors.ff67509C,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),

                  /// Number of total vehicles
                  Padding(
                    padding: EdgeInsets.only(top: AppSize.h16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children:
                          [
                                (
                                  isTrip
                                      ? context.l10n.spotAvailableReservation
                                      : context.l10n.noOfAvailableSlot,
                                  data.availableSlot?.round().toString() ?? '0',
                                  false,
                                ),
                                if (finalSlot != null && finalSlot > 0)
                                  (
                                    isTrip
                                        ? context.l10n.total_trip_cost
                                        : '${context.l10n.totalCostSlot} $finalSlot ${context.l10n.slots}',
                                    (finalSlot * (data.totalCost ?? 0))
                                        .toStringAsFixed(2)
                                        .smartFormat(),
                                    true,
                                  ),
                              ]
                              .map(
                                (e) => TitleInfo(
                                  title: e.$1,
                                  subTitle: e.$2,
                                  subTitleFontWeight: FontWeight.w600,
                                  isAxisEnd: e.$3,
                                  subTitleColor: e.$3
                                      ? AppColors.ff67509C
                                      : null,
                                ),
                              )
                              .toList(),
                    ),
                  ),

                  // Locations and dates row
                  if (!isTrip)
                    Padding(
                      padding: EdgeInsets.only(top: AppSize.h16),
                      child: LocationInfoWidget(
                        startLatitude:
                            data.startStopLocation?.address?.latitude,
                        startLongitude:
                            data.startStopLocation?.address?.longitude,
                        endLatitude: data.endStopLocation?.address?.latitude,
                        endLongitude: data.endStopLocation?.address?.longitude,
                        startLocationTitle:
                            data.customerStartStopLocation?.name ?? '',
                        startLocationDate:
                            data
                                .customerStartStopLocation
                                ?.estimatedArrivalDate
                                ?.monthDateFormate ??
                            '',
                        endLocationTitle:
                            data.customerEndStopLocation?.name ?? '',
                        endLocationDate:
                            data
                                .customerEndStopLocation
                                ?.estimatedArrivalDate
                                ?.monthDateFormate ??
                            '',
                      ),
                    ),
                  Gap(AppSize.h16),
                  Row(
                    spacing: AppSize.w5,
                    children: [
                      _CheckWidget(
                        isCheck: data.isWinch,
                        title: context.l10n.winchSupport,
                      ),
                      _CheckWidget(
                        isCheck: data.isExceptionsSupported,
                        title: context.l10n.exceptionsSupport,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (isDisabled.$1)
              Positioned.fill(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: Container(
                    color: AppColors.greyColor.withValues(alpha: 0.1),
                    alignment: Alignment.center,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                      child: Text(
                        isDisabled.$2,
                        textAlign: TextAlign.center,
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.black,
                          fontWeight: FontWeight.w500,
                          fontSize: AppSize.sp16,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _CheckWidget extends StatelessWidget {
  const _CheckWidget({required this.isCheck, required this.title});
  final bool isCheck;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: AppSize.sp7,
            backgroundColor: isCheck ? Colors.green : AppColors.errorColor,
            child: Icon(
              !isCheck ? Icons.close_rounded : Icons.check_rounded,
              size: AppSize.sp10,
              color: AppColors.white,
            ),
          ),
          Gap(AppSize.w4),
          Flexible(
            child: MarqueeWidget(
              child: Text(
                title,
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.ff6C757D,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
