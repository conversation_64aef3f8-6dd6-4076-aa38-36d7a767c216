import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_padding.dart';

class AppDropdown<T> extends StatefulWidget {
  const AppDropdown({
    required this.items,
    required this.name,
    required this.value,
    this.title,
    this.titleStyle,
    this.validator,
    this.prefixIcon,
    this.hintText,
    this.labelText,
    this.inputFormatters,
    this.keyboardType,
    this.subTitle,
    super.key,
    this.suffix,
    this.suffixIcon,
    this.onChanged,
    this.inputBorder,
    this.fillColor = Colors.white,
    this.titleColor = AppColors.ff343A40,
    // this.focusNode,
    this.fontSize,
    this.contentHeight,
    this.borderRadius,
    this.style,
    this.contentWidth,
    this.hintStyle,
    this.borderSide,
    this.prefix,
    this.isDense,
    this.prefixIconConstraints,
    this.suffixIconConstraints,
    this.enableSearch = true,
  });
  final String? Function(String?)? validator;
  final String? title;
  final String? name;
  final T value;
  final TextStyle? titleStyle;
  final void Function(T)? onChanged;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final List<DropDownValueModel> items;
  final Widget? suffix;
  final String? hintText;
  final String? labelText;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final InputBorder? inputBorder;
  // final FocusNode? focusNode;
  final double? fontSize;
  final double? contentHeight;
  final double? contentWidth;
  final double? borderRadius;
  final Color? fillColor;
  final Color? titleColor;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final BorderSide? borderSide;
  final Widget? prefix;
  final bool? isDense;
  final BoxConstraints? prefixIconConstraints;
  final String? subTitle;
  final BoxConstraints? suffixIconConstraints;
  final bool enableSearch;

  @override
  State<AppDropdown<T>> createState() => _AppDropdownState();
}

class _AppDropdownState<T> extends State<AppDropdown<T>> {
  late SingleValueDropDownController controller;

  @override
  void initState() {
    controller = SingleValueDropDownController(
      data: widget.name != null
          ? DropDownValueModel(name: widget.name ?? '', value: widget.value)
          : null,
    );
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AppDropdown<T> oldWidget) {
    controller = SingleValueDropDownController(
      data: widget.name != null
          ? DropDownValueModel(name: widget.name ?? '', value: widget.value)
          : null,
    );
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Text(
            '  ${widget.title}',
            style:
                widget.titleStyle ??
                context.textTheme.titleSmall!.copyWith(
                  overflow: TextOverflow.ellipsis,
                  fontWeight: FontWeight.w500,
                  color: widget.titleColor,
                ),
          ),
        if (widget.title != null) Gap(AppSize.h4),
        // ValueListenableBuilder(
        //     valueListenable: _isFocused,
        //     builder: (context, isFocused, child){
        //       return
        DropDownTextField(
          controller: controller,
          enableSearch: widget.enableSearch,
          validator: widget.validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          textFieldDecoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(
              horizontal: widget.contentWidth ?? AppSize.w12,
              vertical: widget.contentHeight ?? AppSize.h10,
            ),
            errorMaxLines: 2,
            counterText: '',
            prefixIconConstraints: widget.prefixIconConstraints,
            suffixIconConstraints: widget.suffixIconConstraints,
            isDense: widget.isDense ?? false,
            border:
                widget.inputBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r4,
                  ),
                  borderSide: widget.borderSide ?? BorderSide.none,
                ),
            prefixIcon: widget.prefixIcon == null
                ? null
                : AppPadding(
                    left: AppSize.w14,
                    right: AppSize.w8,
                    child: widget.prefixIcon,
                  ),
            prefix: widget.prefix == null
                ? null
                : AppPadding(
                    left: AppSize.w14,
                    right: AppSize.w8,
                    child: widget.prefix,
                  ),
            suffix: widget.suffix == null
                ? null
                : AppPadding(
                    right: AppSize.w14,
                    left: AppSize.w8,
                    child: widget.suffix,
                  ),
            suffixIcon: widget.suffixIcon == null
                ? null
                : AppPadding(
                    right: AppSize.w14,
                    left: AppSize.w8,
                    child: widget.suffixIcon,
                  ),
            hintText: widget.hintText,
            labelText: widget.labelText,
            floatingLabelStyle: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ffADB5BD,
              fontSize: widget.fontSize ?? AppSize.sp14,
            ),
            labelStyle: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ffADB5BD,
              fontSize: widget.fontSize ?? AppSize.sp14,
            ),
            hintStyle:
                widget.hintStyle ??
                context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.ffADB5BD,
                  fontSize: widget.fontSize ?? AppSize.sp14,
                ),
            errorStyle: TextStyle(
              color: AppColors.errorColor,
              fontSize: widget.fontSize ?? AppSize.sp12,
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r4,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w2, color: AppColors.errorColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r4,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w1, color: AppColors.ffDEE2E6),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r4,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(
                    color: context.theme.primaryColor,
                    width: AppSize.w2,
                  ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r4,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w2, color: AppColors.errorColor),
            ),
            filled: widget.fillColor != null,
            fillColor: widget.fillColor ?? AppColors.ffF8F9FA,
          ),
          searchDecoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(
              horizontal: widget.contentWidth ?? AppSize.w12,
              vertical: widget.contentHeight ?? AppSize.h10,
            ),
            border:
                widget.inputBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r10,
                  ),
                  borderSide: widget.borderSide ?? BorderSide.none,
                ),
            hintText: context.l10n.search,
            labelStyle: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ffADB5BD,
              fontSize: widget.fontSize ?? AppSize.sp14,
            ),
            hintStyle:
                widget.hintStyle ??
                context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.ffADB5BD,
                  fontSize: widget.fontSize ?? AppSize.sp14,
                ),
            errorStyle: TextStyle(
              color: AppColors.errorColor,
              fontSize: widget.fontSize ?? AppSize.sp12,
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r6,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w2, color: AppColors.errorColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r6,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w1, color: AppColors.ffDEE2E6),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r6,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(
                    color: context.theme.primaryColor,
                    width: AppSize.w2,
                  ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? AppSize.r6,
              ),
              borderSide:
                  widget.borderSide ??
                  BorderSide(width: AppSize.w2, color: AppColors.errorColor),
            ),
            filled: widget.fillColor != null,
            fillColor: widget.fillColor ?? AppColors.ffF8F9FA,
          ),
          dropDownList: widget.items,
          onChanged: (value) {
            if (value is DropDownValueModel) {
              widget.onChanged?.call(value.value as T);
            }
          },
          keyboardType: widget.keyboardType,
          textStyle: widget.style ?? context.textTheme.headlineSmall,

          // textFieldFocusNode: widget.focusNode ?? _focusNode,
          listTextStyle:
              widget.style ??
              context.textTheme.headlineSmall?.copyWith(height: 2),
          listPadding: ListPadding(top: AppSize.sp6, bottom: AppSize.sp6),
          searchTextStyle: context.textTheme.headlineSmall,
        ),

        if (widget.subTitle != null) Gap(AppSize.h3),
        if (widget.subTitle != null)
          AppPadding(
            left: AppSize.w10,
            child: Text(
              widget.subTitle!,
              style:
                  widget.titleStyle ??
                  context.textTheme.titleMedium!.copyWith(
                    fontSize: AppSize.sp11,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
            ),
          ),
      ],
    );
  }
}

/// Common Dropdown
class AppDropBottomSheet extends StatefulWidget {
  const AppDropBottomSheet({
    this.labelText,
    this.borderRadius,
    this.items,
    this.selectedItem,
    this.onChanged,
    this.fillColor = AppColors.white,
    this.labelStyle,
    this.style,
    this.textColor,
    this.contentHeight,
    this.contentWidth,
    this.borderSide,
    this.prefixIcon,
    this.suffixIcon,
    this.isDense,
    this.prefixIconConstraints,
    this.suffixIconConstraints,
    super.key,
    this.validator,
  });

  final String? labelText;
  final double? borderRadius;
  final List<Widget>? items;
  final String? selectedItem;
  final ValueChanged<String?>? onChanged;
  final Color? fillColor;
  final TextStyle? labelStyle;
  final TextStyle? style;
  final Color? textColor;
  final double? contentHeight;
  final double? contentWidth;
  final BorderSide? borderSide;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool? isDense;
  final BoxConstraints? prefixIconConstraints;
  final BoxConstraints? suffixIconConstraints;
  final String? Function(String?)? validator;

  @override
  State<AppDropBottomSheet> createState() => _AppDropBottomSheetState();
}

class _AppDropBottomSheetState extends State<AppDropBottomSheet> {
  String? selectedItem;
  @override
  void didUpdateWidget(covariant AppDropBottomSheet oldWidget) {
    setState(() {
      selectedItem = widget.selectedItem;
    });
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => widget.items != null && widget.items!.isNotEmpty
          ? showModalBottomSheet(
              context: context,
              backgroundColor: AppColors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              builder: (context) => Padding(
                padding: EdgeInsets.all(AppSize.bottomSheetPadding),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.items ?? [],
                ),
              ),
            )
          : null,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: AppColors.ffF8F9FA,
          border: Border.all(color: AppColors.ffDEE2E6),
          borderRadius: BorderRadius.circular(AppSize.sp4),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.h16,
            vertical: AppSize.h10,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  selectedItem ?? widget.labelText ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: AppSize.sp14,
                    color: selectedItem != null
                        ? AppColors.ff343A40
                        : AppColors.ffADB5BD,
                    fontWeight: selectedItem != null
                        ? FontWeight.w600
                        : FontWeight.w400,
                  ),
                ),
              ),
              const Icon(Icons.keyboard_arrow_down_outlined),
            ],
          ),
        ),
      ),
    );
  }
}
