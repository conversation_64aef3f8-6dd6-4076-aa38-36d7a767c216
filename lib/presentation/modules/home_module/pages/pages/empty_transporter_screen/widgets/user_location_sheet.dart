import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// Show user location bottom sheet
Future<T?> showLocationBottomSheet<T>(
  BuildContext context,
  HomeProvider homeProvider,
) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: AppColors.pageBGColor,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
    ),
    isScrollControlled: true,
    builder: (context) {
      return Form(
        child: Builder(
          builder: (context) {
            return Padding(
              padding: EdgeInsets.all(AppSize.bottomSheetPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: AppSize.h8,
                children: [
                  Text(
                    context.l10n.userLocation,
                    style: context.textTheme.titleLarge,
                  ),
                  Text(
                    context.l10n.toContinueWithTrip,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontSize: AppSize.sp14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.ff6C757D,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      top: AppSize.h8,
                      bottom: AppSize.h12,
                    ),
                    child: ValueListenableBuilder(
                      valueListenable: homeProvider.exclusivePickLocation,
                      builder: (context, value, child) {
                        return AppTextFormField(
                          controller: TextEditingController(
                            text: value?.address,
                          ),
                          validator: (p0) => commonValidator(
                            errorMessage: context.l10n.pleaseEnterPickLocation,
                            inputValue: p0,
                          ),
                          hintText: context.l10n.userPickUpLocation,
                          readOnly: true,
                          onTap: () => AppNavigationService.pushNamed(
                            context,
                            AppRoutes.homeAddressSearchScreen,
                            extra: AddressSearchParams(
                              hintText: context.l10n.userPickUpLocation,
                              homeProvider: homeProvider,
                              isExclusive: true,
                              isOther: true,
                              onTap: (address) {
                                homeProvider.exclusivePickLocation.value =
                                    address;
                              },
                            ),
                            // AddressSearchScreen(
                            //   hintText: context.l10n.userPickUpLocation,
                            //   homeProvider: homeProvider,
                            //   isExclusive: true,
                            //   isOther: true,
                            //   onTap: (address) {
                            //     homeProvider.exclusivePickLocation.value =
                            //         address;
                            //   },
                            // ),
                          ),
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h16),
                    child: ValueListenableBuilder(
                      valueListenable: homeProvider.exclusiveDropLocation,
                      builder: (context, value, child) {
                        return AppTextFormField(
                          controller: TextEditingController(
                            text: value?.address,
                          ),
                          hintText: context.l10n.userDeliveryLocation,
                          validator: (p0) => commonValidator(
                            errorMessage:
                                context.l10n.pleaseEnterDeliveryLocation,
                            inputValue: p0,
                          ),
                          readOnly: true,
                          onTap: () => AppNavigationService.pushNamed(
                            context,
                            AppRoutes.homeAddressSearchScreen,
                            extra: AddressSearchParams(
                              homeProvider: homeProvider,
                              hintText: context.l10n.userDeliveryLocation,
                              isOther: true,
                              isExclusive: true,
                              onTap: (address) {
                                homeProvider.exclusiveDropLocation.value =
                                    address;
                              },
                            ),
                            // AddressSearchScreen(
                            //   hintText: context.l10n.userDeliveryLocation,
                            //   homeProvider: homeProvider,
                            //   isOther: true,
                            //   onTap: (address) {
                            //     homeProvider.exclusiveDropLocation.value =
                            //         address;
                            //   },
                            // ),
                          ),
                        );
                      },
                    ),
                  ),
                  AppButton(
                    isBottomBtn: false,
                    onPressed: () => Form.of(context).validate()
                        ? homeProvider.bookExclusive(
                            context,
                            homeProvider,
                            isBack: true,
                          )
                        : null,
                    text: context.l10n.save,
                  ),
                ],
              ),
            );
          },
        ),
      );
    },
  );
}
