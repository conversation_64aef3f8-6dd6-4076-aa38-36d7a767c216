import 'package:flutter/material.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_image.dart';
import 'package:transport_match/widgets/app_textfield.dart';

class UserLocationWidget extends StatelessWidget {
  const UserLocationWidget({super.key, required this.homeProvider});
  final HomeProvider homeProvider;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          spacing: AppSize.h16,
          children: [
            ValueListenableBuilder(
              valueListenable: homeProvider.originLocationController,
              builder: (context, value, child) {
                return AppTextFormField(
                  controller: TextEditingController(
                    text: value?.address ?? value?.street,
                  ),
                  hintText: context.l10n.searchOriginLocation,
                  readOnly: true,
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.homeAddressSearchScreen,
                    extra: AddressSearchParams(
                      hintText: context.l10n.searchOriginLocation,
                      homeProvider: homeProvider,
                      isOther: true,
                      isBack: false,
                      onTap: (address) {
                        homeProvider.originLocationController.value =
                            AddressModel(
                              address: address.address,
                              latitude: address.latitude,
                              longitude: address.longitude,
                            );
                      },
                    ),

                    // AddressSearchScreen(
                    //   homeProvider: homeProvider,
                    //   hintText: context.l10n.searchOriginLocation,
                    //   onTap: (address) {
                    //     homeProvider.originLocationController.value =
                    //         AddressModel(
                    //       address: address.address,
                    //       latitude: address.latitude,
                    //       longitude: address.longitude,
                    //     );
                    //   },
                    // ),

                    /// below function is for transfer
                    /// value from address search to menu screen
                    afterBack: (value) {
                      if (value is (AddressModel, String)) {
                        homeProvider.originLocationController.value =
                            AddressModel(
                              address: value.$1.address,
                              latitude: value.$1.latitude,
                              longitude: value.$1.longitude,
                            );
                        homeProvider
                          ..selectedOriginStockLocation.value = homeProvider
                              .originStockLocationList
                              .value
                              .firstWhere((e) => e.id.toString() == value.$2)
                          ..notify();
                      }
                    },
                  ),
                );
              },
            ),
            ValueListenableBuilder(
              valueListenable: homeProvider.dropLocationController,
              builder: (context, value, child) {
                return AppTextFormField(
                  controller: TextEditingController(
                    text: value?.address ?? value?.street,
                  ),
                  readOnly: true,
                  hintText: context.l10n.searchDropLocation,
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.homeAddressSearchScreen,
                    extra: AddressSearchParams(
                      hintText: context.l10n.searchDropLocation,
                      homeProvider: homeProvider,
                      isOther: true,
                      isBack: false,
                      isDrop: true,
                      onTap: (address) {
                        homeProvider.dropLocationController.value =
                            AddressModel(
                              address: address.address,
                              latitude: address.latitude,
                              longitude: address.longitude,
                            );
                      },
                    ),

                    // AddressSearchScreen(
                    //   hintText: context.l10n.searchDropLocation,
                    //   homeProvider: homeProvider,
                    //   isDrop: true,
                    //   onTap: (address) => homeProvider
                    //       .dropLocationController.value = AddressModel(
                    //     address: address.address,
                    //     latitude: address.latitude,
                    //     longitude: address.longitude,
                    //   ),
                    // ),
                    afterBack: (value) {
                      if (value is (AddressModel, String)) {
                        homeProvider.dropLocationController.value =
                            AddressModel(
                              address: value.$1.address,
                              latitude: value.$1.latitude,
                              longitude: value.$1.longitude,
                            );
                        homeProvider
                          ..selectedDropStockLocation.value = homeProvider
                              .dropStockLocationList
                              .value
                              .firstWhere((e) => e.id.toString() == value.$2)
                          ..notify();
                      }
                    },
                  ),
                );
              },
            ),
          ],
        ),
        Align(
          alignment: Alignment.centerRight,
          child: GestureDetector(
            onTap: () {
              if (homeProvider.originLocationController.value == null &&
                  homeProvider.dropLocationController.value == null) {
                return;
              }

              /// for transfer origin and stock location value
              final temp = homeProvider.originLocationController.value;
              homeProvider.originLocationController.value =
                  homeProvider.dropLocationController.value;
              homeProvider.dropLocationController.value = temp;

              /// for transfer selected stock location value
              final tempSelection =
                  homeProvider.selectedOriginStockLocation.value;
              homeProvider.selectedOriginStockLocation.value =
                  homeProvider.selectedDropStockLocation.value;
              homeProvider.selectedDropStockLocation.value = tempSelection;

              /// for transfer stock location list
              final tempList = homeProvider.originStockLocationList.value;
              homeProvider.originStockLocationList.value =
                  homeProvider.dropStockLocationList.value;
              homeProvider.dropStockLocationList.value = tempList;
            },
            child: Container(
              height: AppSize.h40,
              width: AppSize.h40,
              margin: EdgeInsets.only(right: AppSize.sp14),
              padding: EdgeInsets.all(AppSize.sp11),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSize.r16),
                border: Border.all(color: AppColors.ffDEE2E6),
                color: AppColors.white,
              ),
              child: AppImage.asset(AppAssets.iconsSwap.path),
            ),
          ),
        ),
      ],
    );
  }
}
