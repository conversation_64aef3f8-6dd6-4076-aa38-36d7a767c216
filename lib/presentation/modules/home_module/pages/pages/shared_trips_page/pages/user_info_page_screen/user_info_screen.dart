import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/user_info_page_screen/provider/user_info_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/build_pickup_person_info_widget.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// User info page
class UserInfoScreen extends StatelessWidget {
  /// User info
  UserInfoScreen({super.key, required this.bookingId})
    : assert(bookingId.isNotEmptyAndNotNull, 'Booking id is required');
  final String? bookingId;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        context.l10n.pleaseAddBothPickupNDropInfo.showErrorAlert();
      },
      child: ChangeNotifierProvider(
        create: (context) => UserInfoProvider(),
        builder: (context, child) {
          final userInfoProvider = context.read<UserInfoProvider>();
          return Scaffold(
            appBar: CustomAppBar(
              canPop: false,
              title: context.l10n.userInfo,
              leading: const SizedBox(),
            ),
            backgroundColor: AppColors.ffF8F9FA,
            bottomNavigationBar: ValueListenableBuilder(
              valueListenable: userInfoProvider.isShowLoader,
              builder: (context, isShowLoader, child) {
                return AppButton(
                  text: context.l10n.save,
                  onPressed: isShowLoader
                      ? null
                      : () => userInfoProvider.createBookingAssignee(
                          context,
                          bookingId ?? '',
                        ),
                );
              },
            ),
            body: ValueListenableBuilder(
              valueListenable: userInfoProvider.isShowLoader,
              builder: (context, isShowLoader, child) =>
                  AppLoader(isShowLoader: isShowLoader, child: child!),
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
                child: Column(
                  spacing: AppSize.h24,
                  children: [
                    Selector<UserInfoProvider, (String, String)>(
                      selector: (p0, userInfoProvider) => (
                        userInfoProvider.dropUserDocType,
                        userInfoProvider.dropUserImage,
                      ),
                      builder: (context, value, child) {
                        return BuildPickupPersonInfoWidget(
                          isEdit: true,
                          title: context.l10n.carDropPerson,
                          subtitle: context.l10n.thisIsDropInfo,
                          onImageChange: (value) => userInfoProvider
                            ..dropUserImage = value ?? ''
                            ..notify(),
                          name: userInfoProvider.dropUserName,
                          docType: value.$1,
                          onTypeChange: (value) =>
                              userInfoProvider.dropUserDocType = value ?? '',
                          image: value.$2,
                        );
                      },
                    ),
                    Selector<UserInfoProvider, (String, String)>(
                      selector: (p0, userInfoProvider) => (
                        userInfoProvider.pickUserDocType,
                        userInfoProvider.pickUserImage,
                      ),
                      builder: (context, value, child) {
                        return BuildPickupPersonInfoWidget(
                          isEdit: true,
                          title: context.l10n.carPickupPerson,
                          subtitle: context.l10n.thisIsDeliveryInfo,
                          name: userInfoProvider.pickUserName,
                          docType: value.$1,
                          onTypeChange: (value) =>
                              userInfoProvider.pickUserDocType = value ?? '',
                          image: value.$2,
                          onImageChange: (value) => userInfoProvider
                            ..pickUserImage = value ?? ''
                            ..notify(),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
