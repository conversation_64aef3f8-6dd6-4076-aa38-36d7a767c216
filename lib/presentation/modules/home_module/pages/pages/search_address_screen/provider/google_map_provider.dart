import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:transport_match/env/env_app.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/suggestion_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/widget/triangle.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/presentation/modules/home_module/provider/places_api_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

class GoogleMapProvider extends ChangeNotifier {
  /// Controller for custom info window
  final customInfoWindowController = CustomInfoWindowController();

  /// Loading state flag
  bool isShowLoader = false;

  /// List of address suggestions
  List<SuggestionModel> addressList = [];

  /// Flag to check if map is created
  bool isMapCreated = false;

  /// Center position of the map
  LatLng center = const LatLng(19.432608, -99.133209);

  /// Set of markers on the map
  final Set<Marker> markers = {};

  /// Set of stop location markers
  final Set<Marker> stopMarkers = {};

  /// Selected address text
  String? selectedAddress;

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Called when map is created
  /// [controller] is the GoogleMapController instance
  void onMapCreated(GoogleMapController controller) {
    if (_isClosed) return;

    if (!isMapCreated) {
      customInfoWindowController.googleMapController = controller;
      isMapCreated = true;
    }
  }

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Called when camera position changes
  /// [position] is the new camera position
  void onCameraMove(CameraPosition position) {
    if (_isClosed) return;

    customInfoWindowController.onCameraMove?.call();
    notify();
  }

  /// Called when map is tapped
  /// [position] is the tapped position
  /// [isSearch] flag to search for stop locations
  /// [isDrop] flag to indicate if this is a drop location
  /// [homeProvider] is the HomeProvider instance
  /// [context] is the BuildContext
  Future<void> onMapTap(
    LatLng position, {
    required bool isSearch,
    required bool isDrop,
    required HomeProvider homeProvider,
    required BuildContext context,
  }) async {
    '==>>>> lat long $position'.logE;
    if (_isClosed) return;

    try {
      customInfoWindowController.hideInfoWindow!();
      updateMarker(position);
      if (isSearch) {
        await _findStopLocation(
          context: context,
          homeProvider: homeProvider,
          place: position,
          isDrop: isDrop,
        );
      }
      selectedAddress = null;
      notify();
    } catch (e) {
      'onMapTap error: $e'.logE;
    }
  }

  /// Get address suggestions from Google Places API
  /// [search] is the search text
  Future<List<SuggestionModel>> getAddress(String search) async {
    if (_isClosed) return [];

    try {
      return search.isEmpty
          ? []
          : await PlaceApiProvider().fetchSuggestions(
              input: search,
              lang: 'en',
            );
    } catch (e) {
      'getAddress error: $e'.logE;
      return [];
    }
  }

  /// Update marker on the map
  /// [position] is the marker position
  void updateMarker(LatLng position) {
    if (_isClosed) return;

    try {
      markers
        ..clear()
        ..add(
          Marker(
            markerId: const MarkerId('selected_location'),
            position: position,
            icon: BitmapDescriptor.defaultMarkerWithHue(
              BitmapDescriptor.hueRed,
            ),
          ),
        );
      center = position;
    } catch (e) {
      'updateMarker error: $e'.logE;
    }
  }

  /// Add stop location markers on the map
  /// [list] is the list of stop locations
  /// [position] is the current position
  /// [context] is the BuildContext
  Future<void> addMarker(
    List<StopLocationData> list,
    LatLng position,
    BuildContext context,
  ) async {
    if (_isClosed) return;

    try {
      stopMarkers.clear();
      if (_isClosed) return;
      for (final element in list) {
        if (_isClosed) break;

        final lat = double.tryParse(element.address?.latitude ?? '');
        final long = double.tryParse(element.address?.longitude ?? '');
        if (lat != null && long != null) {
          final latLng = LatLng(lat, long);
          stopMarkers.add(
            Marker(
              markerId: MarkerId(element.id.toString()),
              position: latLng,
              icon: await BitmapDescriptor.asset(
                ImageConfiguration.empty,
                'assets/images/pin.png',
                height: 50,
              ),
              onTap: () {
                customInfoWindowController.addInfoWindow!(
                  Column(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(AppSize.r10),
                          ),
                          width: double.infinity,
                          height: double.infinity,
                          padding: EdgeInsets.all(AppSize.sp10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            spacing: AppSize.w8,
                            children: [
                              Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MarqueeWidget(
                                      child: Text(
                                        element.name ?? '',
                                        maxLines: 1,
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                              color: AppColors.white,
                                              fontSize: AppSize.sp14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                      ),
                                    ),
                                    MarqueeWidget(
                                      child: Text(
                                        element.fullAddress ?? '',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                              color: AppColors.white,
                                              fontSize: AppSize.sp12,
                                              fontWeight: FontWeight.w400,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              GestureDetector(
                                onTap: () async {
                                  if (context.mounted) {
                                    isShowLoader = true;
                                    notify();
                                    await getAddressData(
                                      context,
                                      locationId: element.id?.toString(),
                                    );
                                    isShowLoader = false;
                                    notify();
                                  }
                                },
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius: BorderRadius.circular(
                                      AppSize.r10,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                      vertical: AppSize.h2,
                                    ),
                                    child: Text(
                                      context.l10n.select,
                                      style: context.textTheme.bodySmall
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: AppColors.black,
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CustomPaint(
                        size: const Size(20, 10),
                        painter: RPSCustomPainter(),
                      ),
                    ],
                  ),
                  latLng,
                );
              },
            ),
          );
        }
      }
      notify();
    } catch (e) {
      'addMarker error: $e'.logE;
    }
  }

  /// below function is used to move the map to the current location
  /// and to get the current location
  /// [context] is the context of the screen
  /// [isExclusive] is the boolean value to check if the screen is exclusive trip screen
  Future<void> moveToCurrentLocation(BuildContext context) async {
    if (_isClosed) return;
    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (_isClosed) return;
      if (!serviceEnabled) {
        if (context.mounted) {
          context.l10n.locationServicesDisabled.showErrorAlert();
        }
        return;
      }

      // Check location permission
      var permission = await Geolocator.checkPermission();
      if (_isClosed) return;
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (context.mounted) {
            context.l10n.locationPermissionsDenied.showErrorAlert();
          }
          return;
        }
      }

      if (_isClosed) return;
      if (permission == LocationPermission.deniedForever) {
        if (context.mounted) {
          context.l10n.locationPermissionsDeniedForever.showErrorAlert();
        }
        return;
      }

      final position = await Geolocator.getCurrentPosition();
      if (_isClosed) return;

      if (customInfoWindowController.googleMapController != null &&
          context.mounted) {
        unawaited(
          customInfoWindowController.googleMapController!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(position.latitude, position.longitude),
                zoom: 14,
              ),
            ),
          ),
        );

        if (_isClosed) return;
        center = LatLng(position.latitude, position.longitude);
        updateMarker(LatLng(position.latitude, position.longitude));
      }
    } catch (e) {
      if (_isClosed) return;
      log('Error getting current location: $e');
      if (context.mounted) {
        context.l10n.failedToGetLocation.logE;
      }
    }
  }

  /// this function is used when search address text filed element is tapped
  /// and to get stop location around the selected address
  /// [value] is the selected address from the search list
  Future<void> onAddressTap(
    String value,
    BuildContext context, {
    required bool isExclusive,
    required bool isSearchStockLocation,
    required bool isDrop,
    required HomeProvider homeProvider,
  }) async {
    isShowLoader = true;
    notify();
    try {
      selectedAddress = value;
      final place = await PlaceApiProvider().fetchPlaceDetails(
        description: value,
        addressList: addressList,
      );

      if (_isClosed) return;
      if (place != null) {
        if (customInfoWindowController.googleMapController != null &&
            context.mounted) {
          await customInfoWindowController.googleMapController!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(place.latitude, place.longitude),
                zoom: 11,
              ),
            ),
          );
          updateMarker(LatLng(place.latitude, place.longitude));
          if (!isExclusive && isSearchStockLocation && context.mounted) {
            await _findStopLocation(
              context: context,
              homeProvider: homeProvider,
              place: place,
              isDrop: isDrop,
            );
          }
        }
      } else {
        if (_isClosed) return;
        if (context.mounted) {
          context.l10n.selectAnotherAddress.showErrorAlert();
        }
      }
    } catch (e) {
      if (_isClosed) return;
      log('Error: $e');
      if (context.mounted) {
        context.l10n.somethingWentWrong.logE;
      }
    } finally {
      isShowLoader = false;
      notify();
    }
  }

  /// Find stop locations near the selected position
  /// [context] is the BuildContext
  /// [homeProvider] is the HomeProvider instance
  /// [place] is the selected position
  /// [isDrop] flag to indicate if this is a drop location
  Future<void> _findStopLocation({
    required BuildContext context,
    required HomeProvider homeProvider,
    required LatLng place,
    required bool isDrop,
  }) async {
    if (_isClosed) return;

    try {
      final val = await homeProvider.listNearByLocation(
        context: context,
        isDrop: isDrop,
        latLng: center,
      );

      if (!_isClosed && val.isNotEmpty) {
        await addMarker(
          val,
          LatLng(place.latitude, place.longitude),
          // ignore: use_build_context_synchronously
          context,
        );
      }
    } catch (e) {
      '_findStopLocation error: $e'.logE;
    }
  }

  /// Handle button tap to select location
  /// [context] is the BuildContext
  /// [onTap] callback when address is selected
  /// [isBack] flag to navigate back
  Future<void> onBtnTap(
    BuildContext context, {
    required Function(AddressModel address) onTap,
    bool isBack = true,
  }) async {
    if (_isClosed) return;

    if (!isMapCreated || markers.isEmpty) {
      context.l10n.selectLocationOnMap.showErrorAlert();
      return;
    }

    isShowLoader = true;
    notify();

    try {
      await getAddressData(context, onTap: onTap, isBack: isBack);
      if (_isClosed) return;
    } catch (e) {
      if (_isClosed) return;
      'onBtnTap error: $e'.logE;
      if (context.mounted) {
        context.l10n.failedToGetLocationDetails.showErrorAlert();
      }
    } finally {
      if (context.mounted && !_isClosed) {
        isShowLoader = false;
        notify();
      }
    }
  }

  /// Get address data from coordinates
  /// [context] is the BuildContext
  /// [onTap] callback when address is selected
  /// [locationId] optional location ID
  /// [isBack] flag to navigate back
  Future<AddressModel?> getAddressData(
    BuildContext context, {
    Function(AddressModel address)? onTap,
    String? locationId,
    bool isBack = true,
  }) async {
    if (_isClosed) return null;

    try {
      final placeMarks = await PlaceApiProvider().getAccurateAddress(
        latitude: center.latitude,
        longitude: center.longitude,
        isClosed: _isClosed,
        selectedAddress: selectedAddress,
        googleApiKey: Platform.isAndroid
            ? AppEnv().googleMapAndroidKey
            : AppEnv().googleMapIosKey,
      );

      if (_isClosed) return null;
      AddressModel? address;

      if (placeMarks !=null) {
        address = placeMarks;
        // final addressText =
        //     selectedAddress ??
        //     '${placeMarks.first.name}, ${placeMarks.first.locality},'
        //         ' ${placeMarks.first.administrativeArea}, ${placeMarks.first.country}';
        // final placemark = placeMarks.first;
        // address = AddressModel(
        //   latitude: center.latitude.toString(),
        //   longitude: center.longitude.toString(),
        //   address: addressText,
        //   city: placemark.locality,
        //   country: placemark.country,
        //   postalCode: placemark.postalCode,
        //   state: placemark.administrativeArea,
        //   countryCode: placemark.isoCountryCode,
        //   street: addressText,
        // );

        if (_isClosed) return null;
        if (!_isClosed) onTap?.call(address);
        if (isBack && context.mounted && !_isClosed) {
          Navigator.pop(context, (address, locationId));
        }
      } else {
        if (_isClosed) return null;
        if (context.mounted) {
          context.l10n.failedToGetLocationDetails.showErrorAlert();
        }
      }
      return address;
    } catch (e) {
      if (_isClosed) return null;
      'getAddressData error: $e'.logE;
      if (context.mounted) {
        context.l10n.failedToGetLocationDetails.showErrorAlert();
      }
      return null;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    notify();
    customInfoWindowController.dispose();

    super.dispose();
  }
}
