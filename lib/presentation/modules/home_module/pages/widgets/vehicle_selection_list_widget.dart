import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_selection_widget.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class VehicleSelectionListWidget extends StatelessWidget {
  const VehicleSelectionListWidget({super.key, this.isExclusiveTrip = false});
  final bool isExclusiveTrip;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h16),
      child: Consumer<HomeProvider>(
        builder: (context, homeProvider, child) {
          return Column(
            spacing: AppSize.h16,
            children: [
              ListView.separated(
                itemCount: homeProvider.selectedVehicleInfo.length,
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final vehicle = homeProvider.selectedVehicleInfo[index];
                  return VehicleSelectorWidget(
                    selectedBrand: vehicle.selectedBrand,
                    isExclusiveTrip: isExclusiveTrip,
                    carType: vehicle.carType,
                    carBrands: homeProvider.carBrands,
                    isWinchRequired: vehicle.isWinchRequired,
                    carYearList: vehicle.carYearList ?? [],
                    selectedCarYear: vehicle.selectedCarYear,
                    carBrandString: vehicle.carBrandString,
                    carIssueString: vehicle.carIssueString,
                    carModelString: vehicle.carModelString,
                    carSerialString: vehicle.carSerialString,
                    carYearString: vehicle.carYearString,
                    carModels: vehicle.carModels ?? [],
                    selectedCarModel: vehicle.selectedCarModel,
                    showManualInputs: vehicle.showManualInputs,
                    isCarShouldBePickedUpFromMyLocation:
                        vehicle.isCarShouldBePickedUpFromMyLocation,
                    costId: vehicle.costId,
                    pickupCostModel: vehicle.pickupCostModel,
                    onCarWinchRequiredChanged: ({required bool value}) {
                      homeProvider
                        ..selectedVehicleInfo[index].isWinchRequired = value
                        ..notify();
                    },
                    onCarBrandStringChanged: (value) {
                      homeProvider
                        ..selectedVehicleInfo[index].carBrandString = value
                        ..notify();
                    },
                    // onCarConditionStringChanged: (value) {
                    //   homeProvider
                    //     ..selectedVehicleInfo[index].carConditionString = value
                    //     ..notify();
                    // },
                    onCarModelStringChanged: (value) {
                      homeProvider
                        ..selectedVehicleInfo[index].carModelString = value
                        ..notify();
                    },
                    onCarSerialStringChanged: (value) {
                      homeProvider
                        ..selectedVehicleInfo[index].carSerialString = value
                        ..notify();
                    },
                    onCarYearStringChanged: (value) {
                      homeProvider
                        ..selectedVehicleInfo[index].carYearString = value
                        ..notify();
                    },
                    onCarIssueStringChanged: (value) {
                      homeProvider
                        ..selectedVehicleInfo[index].carIssueString = value
                        ..notify();
                    },
                    onCarBrandSelected: (item) {
                      homeProvider.onCarBrandSelected(item, index);
                    },
                    onCarYearSelected: (item) {
                      homeProvider.onCarYearSelected(item, index);
                    },
                    onCarModelSelected: (item) {
                      homeProvider.onCarModelSelected(item, index);
                    },
                    onCarTypeChanged: (value) => homeProvider
                      ..selectedVehicleInfo[index].carType?.value= value
                      ..notify(),

                    onManualInputChanged: ({required bool value}) {
                      homeProvider.onManualInputChanged(value, index);
                    },
                    onDeleteButtonClick:
                        homeProvider.selectedVehicleInfo.length > 1
                        ? () {
                            homeProvider.removeVehicle(index);
                          }
                        : null,
                    onCarShouldBePickedUpFromMyLocationChanged:
                        ({required bool value}) {
                          homeProvider
                            ..selectedVehicleInfo[index]
                                    .isCarShouldBePickedUpFromMyLocation =
                                value
                            ..getCostOfPickUp()
                            ..notify();
                        },
                  );
                },
                separatorBuilder: (context, index) {
                  return Gap(AppSize.h16);
                },
              ),
              GestureDetector(
                onTap: homeProvider.addVehicle,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: 5,
                  children: [
                    Icon(
                      Icons.add_circle,
                      color: Colors.blue,
                      size: AppSize.sp20,
                    ),
                    Text(
                      context.l10n.addAnotherVehicle,
                      style: TextStyle(
                        fontSize: AppSize.sp14,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
