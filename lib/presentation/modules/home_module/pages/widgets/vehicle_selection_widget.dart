import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/car_creation_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/pickup_cost_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_type_widget.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/app_radio_button.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// Vehicle information UI
class VehicleSelectorWidget extends StatefulWidget {
  /// Constructor
  const VehicleSelectorWidget({
    required this.carBrands,
    required this.selectedBrand,
    required this.onCarBrandSelected,
    required this.carYearList,
    required this.selectedCarYear,
    required this.onCarYearSelected,
    required this.carModels,
    required this.selectedCarModel,
    required this.onCarModelSelected,
    required this.showManualInputs,
    required this.onManualInputChanged,
    required this.onCarShouldBePickedUpFromMyLocationChanged,
    this.onDeleteButtonClick,
    this.onCarBrandStringChanged,
    this.onCarModelStringChanged,
    this.onCarYearStringChanged,
    this.onCarSerialStringChanged,
    this.onCarIssueStringChanged,
    this.carBrandString,
    this.carModelString,
    this.carYearString,
    this.carIssueString,
    this.carSerialString,
    this.carType,
    super.key,
    this.onCarTypeChanged,
    this.onCarWinchRequiredChanged,
    this.isWinchRequired = false,
    this.isExclusiveTrip = false,
    this.isCarShouldBePickedUpFromMyLocation = false,
    this.costId,
    this.pickupCostModel,
  });

  final bool isExclusiveTrip;

  /// Vehicle brand list
  final List<CarCreationModel> carBrands;

  /// selected car brand
  final CarCreationModel? selectedBrand;

  /// On select car brand
  final void Function(CarCreationModel selectedItem) onCarBrandSelected;

  /// Car information
  final List<CarYearModel> carYearList;

  /// Selected car creation
  final CarYearModel? selectedCarYear;

  /// On select car creation
  final void Function(CarYearModel selectedItem) onCarYearSelected;

  /// Vehicle model
  final List<CarModel> carModels;

  /// Selected car model
  final CarModel? selectedCarModel;

  /// On select car model
  final void Function(CarModel selectedItem) onCarModelSelected;

  /// On delete button click
  final VoidCallback? onDeleteButtonClick;

  /// Show manual inputs
  final bool showManualInputs;

  /// On select car model
  final void Function({required bool value}) onManualInputChanged;

  /// On car brand string change
  final void Function(String value)? onCarBrandStringChanged;

  /// On car model string change
  final void Function(String value)? onCarModelStringChanged;

  /// On car year string change
  final void Function(String value)? onCarYearStringChanged;

  /// On car serial string change
  final void Function(String value)? onCarSerialStringChanged;

  /// On can issues description change
  final void Function(String value)? onCarIssueStringChanged;

  /// On can type changed
  final void Function(String? value)? onCarTypeChanged;

  /// On car winch required changed
  final void Function({required bool value})? onCarWinchRequiredChanged;

  /// On car should be picked up from my location changed
  final void Function({required bool value})?
      onCarShouldBePickedUpFromMyLocationChanged;

  /// car brand string current value
  final String? carBrandString;

  /// car model string current value
  final String? carModelString;

  /// car year string current value
  final String? carYearString;

  /// car creation string current value
  final String? carIssueString;

  /// car serial string current value
  final String? carSerialString;

  /// car winch required
  final bool isWinchRequired;

  /// for car sizing
  final ValueNotifier<String?>? carType;

  /// for car cost id
  final ValueNotifier<int?>? costId;

  /// for car should be picked up from my location
  final bool isCarShouldBePickedUpFromMyLocation;

  /// for car pickup cost model
  final ValueNotifier<PickupCostModel?>? pickupCostModel;

  @override
  State<VehicleSelectorWidget> createState() => _VehicleSelectorWidgetState();
}

class _VehicleSelectorWidgetState extends State<VehicleSelectorWidget> {
   TextEditingController vehicleBrandController = TextEditingController();
  late TextEditingController vehicleModelController;
  late TextEditingController vehicleYearController;
  late TextEditingController vehicleIssueController;
  late TextEditingController vehicleSerialController;
  late SingleValueDropDownController carBrandController;
  late SingleValueDropDownController carModelController;
  late SingleValueDropDownController carYearController;

  bool isExpanded = false;
  @override
  void initState() {
    super.initState();
    vehicleBrandController = TextEditingController(text: widget.carBrandString);
    vehicleModelController = TextEditingController(text: widget.carModelString);
    vehicleYearController = TextEditingController(text: widget.carYearString);
    vehicleIssueController = TextEditingController(text: widget.carIssueString);
    vehicleSerialController = TextEditingController(
      text: widget.carSerialString,
    );

    /// assign value to dropdown controller
    carBrandController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedBrand?.carBrand ?? '',
        value: widget.selectedBrand,
      ),
    );
    carModelController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedCarModel?.model ?? '',
        value: widget.selectedCarModel,
      ),
    );
    carYearController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedCarYear?.year.toString() ?? '',
        value: widget.selectedCarYear,
      ),
    );
  }

  @override
  void didUpdateWidget(covariant VehicleSelectorWidget oldWidget) {
    /// assign value to dropdown controller
    carBrandController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedBrand?.carBrand ?? '',
        value: widget.selectedBrand,
      ),
    );
    carModelController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedCarModel?.model ?? '',
        value: widget.selectedCarModel,
      ),
    );
    carYearController = SingleValueDropDownController(
      data: DropDownValueModel(
        name: widget.selectedCarYear?.year.toString() ?? '',
        value: widget.selectedCarYear,
      ),
    );
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    vehicleBrandController.dispose();
    vehicleModelController.dispose();
    vehicleYearController.dispose();
    vehicleIssueController.dispose();
    vehicleSerialController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.h16),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.vehicleInfo,
                style: context.textTheme.titleLarge,
              ),
              if (widget.onDeleteButtonClick != null)
                GestureDetector(
                  onTap: () {
                    widget.onDeleteButtonClick?.call();
                  },
                  child: AppAssets.iconsDelete.image(
                    width: AppSize.h24,
                    height: AppSize.h24,
                  ),
                ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(AppSize.h16),

              /// when user vehicles are not available in our database
              if (!widget.showManualInputs) ...[
                AppDropdown<CarCreationModel?>(
                  items: widget.carBrands
                      .map(
                        (e) => DropDownValueModel(
                          name: e.carBrand ?? '',
                          value: e,
                        ),
                      )
                      .toList(),
                  name: widget.selectedBrand?.carBrand,
                  title: context.l10n.vehicleBrand,
                  value: widget.selectedBrand,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVBrandName,
                    inputValue: p0,
                  ),
                  onChanged: (p0) {
                    if (p0 != null) {
                      widget.onCarBrandSelected(p0);
                    }
                  },
                  hintText: context.l10n.chooseVehicleBrand,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppDropdown<CarYearModel?>(
                    items: widget.carYearList
                        .map(
                          (e) => DropDownValueModel(
                            name: e.year.toString(),
                            value: e,
                          ),
                        )
                        .toList(),
                    title: context.l10n.vehicleYear,
                    name: widget.selectedCarYear?.year.toString(),
                    value: widget.selectedCarYear,
                    validator: (p0) => commonValidator(
                      errorMessage: context.l10n.chooseVYear,
                      inputValue: p0,
                    ),
                    onChanged: (p0) {
                      if (p0 != null) {
                        widget.onCarYearSelected(p0);
                      }
                    },
                    hintText: context.l10n.chooseVehicleYear,
                  ),
                ),
                AppDropdown<CarModel?>(
                  items: widget.carModels
                      .map((e) => DropDownValueModel(name: e.model, value: e))
                      .toList(),
                  title: context.l10n.vehicleModel,
                  name: widget.selectedCarModel?.model,
                  value: widget.selectedCarModel,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.chooseVModel,
                    inputValue: p0,
                  ),
                  onChanged: (p0) {
                    if (p0 != null) {
                      widget.onCarModelSelected(p0);
                    }
                  },
                  hintText: context.l10n.chooseVehicleModel,
                ),
              ] else ...[
                Text(
                  context.l10n.vehicleType,
                  style: context.textTheme.titleSmall,
                ),
                Gap(AppSize.h4),
                ValueListenableBuilder(
                  valueListenable: widget.carType ?? ValueNotifier(null),
                  builder: (context, value, child) {
                    return VehicleTypeWidget(
                      type: value ?? AppStrings.carHauler,
                      onChange: (value) {
                        if(value != null){
                          widget.onCarTypeChanged?.call(value);
                        }
                      }
                    );
                  },
                ),
                AppTextFormField(
                  controller: vehicleBrandController,
                  title: context.l10n.vehicleBrand,
                  hintText: context.l10n.enterVehicleBrand,
                  textAction: TextInputAction.next,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVBrandName,
                    inputValue: p0,
                  ),
                  onChanged: (value) {
                    if (value != null) {
                      widget.onCarBrandStringChanged?.call(value);
                    }
                  },
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppTextFormField(
                    controller: vehicleYearController,
                    title: context.l10n.vehicleYear,
                    hintText: context.l10n.enterVehicleYearLabel,
                    textAction: TextInputAction.next,
                    keyboardType: TextInputType.phone,
                    validator: (p0) => commonValidator(
                      errorMessage: context.l10n.enterVYear,
                      inputValue: p0,
                    ),
                    readOnly: true,
                    onTap: () async {
                      await showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: Text(
                              context.l10n.vehicleYear,
                              style: context.textTheme.titleSmall,
                            ),
                            content: SizedBox(
                              width: AppSize.sp300,
                              height: AppSize.sp300,
                              child: YearPicker(
                                firstDate: DateTime(1900),
                                lastDate: vehicleYearController
                                        .text.isNotEmptyAndNotNull
                                    ? DateTime(
                                        int.parse(vehicleYearController.text),
                                      )
                                    : DateTime.now(),
                                selectedDate: vehicleYearController
                                        .text.isNotEmptyAndNotNull
                                    ? DateTime(
                                        int.parse(vehicleYearController.text),
                                      )
                                    : DateTime.now(),
                                onChanged: (DateTime dateTime) {
                                  Navigator.pop(context);
                                  vehicleYearController.text =
                                      dateTime.year.toString();
                                  widget.onCarYearStringChanged
                                      ?.call(dateTime.year.toString());
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    maxTextLength: 4,
                    // onChanged: (value) {
                    //   if (value != null) {
                    //     widget.onCarYearStringChanged?.call(value);
                    //   }
                    // },
                  ),
                ),
                AppTextFormField(
                  title: context.l10n.vehicleModel,
                  controller: vehicleModelController,
                  validator: (p0) => commonValidator(
                    errorMessage: context.l10n.enterVModel,
                    inputValue: p0,
                  ),
                  hintText: context.l10n.enterVehicleModelLabel,
                  textAction: TextInputAction.next,
                  onChanged: (value) {
                    if (value != null) {
                      widget.onCarModelStringChanged?.call(value);
                    }
                  },
                ),
              ],

              Padding(
                padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                child: AppTextFormField(
                  title: context.l10n.pleaseDescribeTheIssue,
                  controller: vehicleIssueController,
                  hintText: context.l10n.writeIssueDetailsHere,
                  labelColor: AppColors.ffADB5BD,
                  textAction: TextInputAction.next,
                  onChanged: (value) {
                    if (value != null) {
                      widget.onCarIssueStringChanged?.call(value);
                    }
                  },
                ),
              ),

              AppTextFormField(
                controller: vehicleSerialController,
                title: context.l10n.vehicleSerialNo,
                maxTextLength: 100,
                hintText: context.l10n.vehicleSerialNumber,
                validator: (p0) => commonValidator(
                  errorMessage: context.l10n.pleaseEnterVSerialNumber,
                  inputValue: p0,
                ),
                labelColor: AppColors.ffADB5BD,
                onChanged: (value) {
                  if (value != null) {
                    widget.onCarSerialStringChanged?.call(value);
                  }
                },
              ),
              if (!widget.isExclusiveTrip) ...[
                Padding(
                  padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                  child: AppConfirmCheckBox(
                    description: context.l10n.winchRequired,
                    value: widget.isWinchRequired,
                    onSelectionChanged: ({required bool value}) {
                      if (value &&
                          !widget.isCarShouldBePickedUpFromMyLocation) {
                        widget.onCarShouldBePickedUpFromMyLocationChanged?.call(
                          value: value,
                        );
                      }
                      if (widget.costId != null &&
                          widget.costId?.value !=
                              widget.pickupCostModel?.value?.results.first.id) {
                        widget.costId?.value =
                            widget.pickupCostModel?.value?.results.first.id;
                      }
                      widget.onCarWinchRequiredChanged?.call(value: value);
                    },
                  ),
                ),
                AppConfirmCheckBox(
                  value: widget.isCarShouldBePickedUpFromMyLocation,
                  description: context
                      .l10n.iNeedMyCarToBePickedUpAndTakenToTheStockLocation,
                  isDisabledValueChange: widget.isWinchRequired,
                  onSelectionChanged: ({required bool value}) {
                    if (widget.isWinchRequired && !value) {
                      context.l10n
                          .youHaveOptForWinchServiceSoYouCanNotDisableThisOption
                          .showInfoAlert();
                    } else {
                      widget.onCarShouldBePickedUpFromMyLocationChanged?.call(
                        value: value,
                      );
                    }
                  },
                ),
              ],
              if (widget.isCarShouldBePickedUpFromMyLocation)
                ValueListenableBuilder(
                  valueListenable:
                      widget.pickupCostModel ?? ValueNotifier(null),
                  builder: (context, pickupCostModel, child) {
                    if (widget.costId != null) {
                      return Column(
                        spacing: AppSize.h12,
                        children: [
                          ValueListenableBuilder(
                            valueListenable:
                                widget.costId ?? ValueNotifier(null),
                            builder: (context, value, child) {
                              return pickupCostModel != null &&
                                      pickupCostModel.results.isNotEmpty
                                  ? SizedBox(
                                      width: context.width,
                                      child: GridView.builder(
                                        shrinkWrap: true,
                                        padding: EdgeInsets.only(
                                          top: AppSize.h10,
                                        ),
                                        itemCount:
                                            pickupCostModel.results.length,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          childAspectRatio: 2,
                                        ),
                                        itemBuilder:
                                            (context, pickupCostIndex) {
                                          final result = pickupCostModel
                                              .results[pickupCostIndex];
                                          final isDisabled =
                                              widget.isWinchRequired &&
                                                  pickupCostIndex == 1;

                                          return Opacity(
                                            opacity: isDisabled ? 0.5 : 1.0,
                                            child: IgnorePointer(
                                              ignoring: isDisabled,
                                              child: AppRadioButton(
                                                index: pickupCostIndex,
                                                title: result.serviceType
                                                    .replaceAll('_', ' ')
                                                    .capitalized,
                                                subtitle:
                                                    '\$ ${result.minimumFee}',
                                                subtitle2:
                                                    '\$ ${result.serviceFee}/km',
                                                isSelected: value == result.id,
                                                onChanged: (index) {
                                                  if (!isDisabled) {
                                                    widget.costId?.value =
                                                        result.id;
                                                  }
                                                },
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : const SizedBox();
                            },
                          ),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '${context.l10n.note}: ',
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.ff6C757D,
                                  ),
                                ),
                                TextSpan(
                                  text: context.l10n
                                      .asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.ff6C757D,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }
                    return const SizedBox();
                  },
                ),
              Gap(AppSize.h16),
              AppConfirmCheckBox(
                description: context.l10n.didNtFoundCar,
                onSelectionChanged: widget.onManualInputChanged,
                value: widget.showManualInputs,
              ),
            ],
          ),
          // AnimatedCrossFade(
          //   crossFadeState: CrossFadeState.showSecond,
          //   duration: Durations.medium4,
          //   firstChild: const SizedBox.shrink(),
          //   secondChild: ,
          // ),
        ],
      ),
    );
  }
}
