import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/rested_requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/presentation/modules/notifications_module/models/notification_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class NotificationProvider extends ChangeNotifier {
  NotificationProvider() {
    getNotification(isWantShowLoader: true);
  }

  String getGroupLabel(DateTime date) {
    final l10n = rootNavKey.currentContext!.l10n;

    final localDate = date.toLocal();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(localDate.year, localDate.month, localDate.day);

    if (notificationDate == today) return l10n.today;
    if (notificationDate == yesterday) return l10n.yesterday;

    return DateFormat('EEE, d MMM yyyy').format(notificationDate);
  }


  /// Flag to check if provider is closed
  final bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Smart refresh controller
  final refreshController = RefreshController();

  /// List of notifications
  List<NotificationModel> notificationList = [];

  /// Loading state flag
  bool isShowLoader = false;

  /// Token for notification API
  CancelToken? notificationCancelToken;

  /// Next page URL for pagination
  String? nextUrl;

  /// Get notifications from API
  /// [isPagination] flag for pagination
  /// [isWantShowLoader] flag to show loader
  Future<void> getNotification({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (_isClosed) return;

    notificationCancelToken?.cancel();
    notificationCancelToken = CancelToken();
    if (isWantShowLoader) isShowLoader = true;
    if (notificationCancelToken?.isCancelled ?? true) return;
    notify();

    try {
      final response = await Injector.instance<AccountRepository>()
          .getNotification(
            ApiRequest(
              path: isPagination
                  ? nextUrl ?? EndPoints.notifications
                  : EndPoints.notifications,
              cancelToken: notificationCancelToken,
            ),
          );

      if (_isClosed) return;

      response.when(
        success: (data) {
          if (_isClosed || (notificationCancelToken?.isCancelled ?? true)) {
            return;
          }

          nextUrl = data.next;
          isShowLoader = false;
          if (!isPagination) notificationList.clear();
          final dummyList = notificationList + (data.results ?? []);
          notificationList = dummyList;
          notify();
        },
        error: (error) {
          if (_isClosed || (notificationCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader = false;
          notify();
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (notificationCancelToken?.isCancelled ?? true)) {
        return;
      }

      isShowLoader = false;
      notify();
      'getNotification error: $e'.logE;
    }
  }

  Future<void> onNotificationTap({
    required Map<String, dynamic> notificationData,
    required BuildContext context,
    required String notificationType,
  }) async {
    final data = notificationData;
    '===>>>> notification data $data'.logE;
    switch (notificationType) {
      /// when notification type is checklist
      case AppStrings.checkList:
        {
          await AppNavigationService.pushNamed(
            context,
            AppRoutes.tripsChecklistScreen,
            extra: ChecklistParams(
              carId: (data[AppStrings.bookedCarId] as String?) ?? '',
              clientName: (data[AppStrings.clientName] as String?) ?? '',
              checkListId: (data[AppStrings.relatedObjectId] as String?) ?? '',
            ),

            // extra:
            // ChecklistScreen(
            //   carId: (data[AppStrings.bookedCarId] as String?) ?? '',
            //   clientName: (data[AppStrings.clientName] as String?) ?? '',
            //   checkListId: (data[AppStrings.relatedObjectId] as String?) ?? '',
            // ),
          );
        }

      /// when notification type is trip
      case AppStrings.booking ||
          AppStrings.bookingDetail ||
          AppStrings.trip ||
          AppStrings.car:
        // '==>>> notification: $data \n ${data[AppStrings.bookingType]}'.logE;

        // Check if not_to_redirect is "True" - if so, don't redirect
        final notToRedirect = data[AppStrings.notToRedirect]?.toString().toLowerCase();
        if (notToRedirect == 'true') {
          return;
        } else {
          /// when notification booking type is requested trip
          if (data[AppStrings.bookingStatus] ==
              NotificationBookingType.EXCLUSIVE.name ||
              data[AppStrings.bookingStatus] ==
                  NotificationBookingType.WAITING_LIST.name) {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsRequestedTripsScreen,
              extra: RequestedTripsParams(
                tripId: data[AppStrings.bookingId]?.toString() ?? '0',
                // isWaitingList:
                //     data[AppStrings.relatedObjectType] != AppStrings.exclusive,
              ),
            );

            /// use for rested trip
          } else if (data[AppStrings.bookingStatus] ==
              NotificationBookingType.RESTED.name) {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsRestedRequestedTripsScreen,
              extra: RestedRequestedTripsParams(
                value: 2,
                id:
                    data[AppStrings.id]?.toString() ??
                    data[AppStrings.bookingId]?.toString() ??
                    '0',
                requestedTripsProvider: RequestedTripsProvider(),
              ),
            );
          } else {
            /// other wise navigate to trip detail screen
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsTripDetailsScreen,
              extra: TripDetailsParams(
                isCompleted: false,
                id:
                    data[AppStrings.bookingId]?.toString() ??
                    data[AppStrings.relatedObjectId]?.toString() ??
                    '',
              ),
            );
          }
        }
    }
  }
}
