import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';

/// Parameter model for shipment confirmation screen.
class ShipmentConfirmationParams {
  /// Constructor
  const ShipmentConfirmationParams({
    required this.bookingProvider,
    required this.homeProvider,
    this.pickupTime,
    this.dropTime,
  });

  /// Booking provider instance
  final BookingProvider bookingProvider;

  /// Home provider instance
  final HomeProvider homeProvider;

  /// Pickup time
  final String? pickupTime;

  /// Drop time
  final String? dropTime;
}
