import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
import 'package:transport_match/presentation/modules/booking_module/widgets/contact_detail_widget/contact_detail_widget.dart';
import 'package:transport_match/presentation/modules/booking_module/widgets/stock_vehicles_info_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Stock location page
class StockLocationsScreen extends StatefulWidget {
  /// Constructor
  const StockLocationsScreen({
    super.key,
    required this.homeProvider,
    required this.transporterDetail,
    required this.bookingSessionId,
    required this.oldId,
  });
  final HomeProvider homeProvider;
  final List<TransporterDetailModel> transporterDetail;
  final String bookingSessionId;
  final int? oldId;

  @override
  State<StockLocationsScreen> createState() => _StockLocationsScreenState();
}

class _StockLocationsScreenState extends State<StockLocationsScreen> {
  @override
  Widget build(BuildContext context) {
    final startLocation =
        widget.homeProvider.selectedOriginStockLocation.value?.address;
    final dropLocation =
        widget.homeProvider.selectedDropStockLocation.value?.address;
    return ChangeNotifierProvider(
      create: (context) => BookingProvider(
        widget.transporterDetail,
        bookingSessionId: widget.bookingSessionId,
        oldIdValue: widget.oldId,
      ),
      builder: (context, child) {
        final bookingProvider = context.read<BookingProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.stockLocations),
          backgroundColor: AppColors.ffF8F9FA,
          body: ValueListenableBuilder(
            valueListenable: bookingProvider.isShowLoader,
            builder: (context, value, child) =>
                AppLoader(isShowLoader: value, child: child!),
            child: SingleChildScrollView(
              padding: EdgeInsets.all(AppSize.appPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _LocationTile(
                    title: context.l10n.pickupLocation,
                    subtitle: bookingProvider.fullAddress(startLocation),
                    trailing: startLocation?.city ?? '',
                    heading: AppAssets.iconsLocationOrigin.image(
                      height: AppSize.h22,
                    ),
                  ),
                  Gap(AppSize.h16),
                  _LocationTile(
                    title: context.l10n.deliveryLocation,
                    subtitle: bookingProvider.fullAddress(dropLocation),
                    trailing: dropLocation?.city ?? '',
                    heading: AppAssets.iconsLocation.image(height: AppSize.h22),
                  ),
                  Gap(AppSize.h24),
                  ValueListenableBuilder(
                    valueListenable: bookingProvider.isDisclaimerAccepted,
                    builder: (context, isDisclaimerAccepted, child) {
                      return Row(
                        spacing: AppSize.w8,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h2),
                            child: AppConfirmCheckBox(
                              onSelectionChanged: ({required value}) =>
                                  bookingProvider.isDisclaimerAccepted.value =
                                      value,
                              value: isDisclaimerAccepted,
                            ),
                          ),
                          Flexible(
                            fit: FlexFit.tight,
                            child: RichText(
                              text: TextSpan(
                                text: context.l10n.anyUndeclared,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontSize: AppSize.sp12,
                                  fontWeight: FontWeight.w400,
                                ),
                                children: [
                                  TextSpan(
                                    text: context.l10n.withoutRefund,
                                    style: context.textTheme.bodyMedium
                                        ?.copyWith(
                                          fontSize: AppSize.sp12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                  TextSpan(
                                    text: context.l10n.itIsUserResponsibility,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  Gap(AppSize.h24),
                  if (widget.homeProvider.isPickUpSelected)
                    const ContactDetailWidget(),
                  Text(
                    context.l10n.providerInfo,
                    style: context.textTheme.titleSmall?.copyWith(
                      fontSize: AppSize.sp16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Gap(AppSize.h8),
                  Column(
                    children: List.generate(
                      bookingProvider.transporterDetailList.value.length,
                      (index) => StockVehiclesInfoWidget(
                        providerData:
                            bookingProvider.transporterDetailList.value[index],
                        isFirstScreen: true,
                        mainIndex: index,
                        bookingProvider: bookingProvider,
                        homeProvider: widget.homeProvider,
                        // onClose: (){},
                      ),
                    ),
                  ),
                  Gap(AppSize.h16),
                  Text(
                    context.l10n.notePleaseDrop,
                    style: context.textTheme.titleSmall?.copyWith(
                      fontSize: AppSize.sp12,
                      color: AppColors.ff6C757D,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Gap(AppSize.h24),
                ],
              ),
            ),
          ),
          bottomNavigationBar: AppButton(
            onPressed: () async {
              final userDetail =
                  Injector.instance<AppDB>().userModel?.user?.userDetailData;
              if (widget.homeProvider.isPickUpSelected &&
                  (userDetail?.bookingNumber.isEmptyOrNull ?? true)) {
                context.l10n.pleaseEnterUrMobileNumber.showErrorAlert();
                return;
              }

              if (!bookingProvider.isDisclaimerAccepted.value) {
                context.l10n.pleaseAcceptDeclaration.showErrorAlert();
                return;
              }

              if (bookingProvider.transporterDetailList.value.any(
                (e) => e.carDetails.any((c) => c.dropOffDate == null),
              )) {
                context.l10n.pleaseSelectDropDate.showErrorAlert();
              } else {
                if (bookingProvider.transporterDetailList.value.any(
                  (e) => e.carDetails.any(
                    (c) => c.isInsuranceIncluded && (c.insurance.isEmptyOrNull),
                  ),
                )) {
                  context.l10n.pleaseSelectInsurance.showErrorAlert();
                } else {
                  await AppNavigationService.pushNamed(
                    context,
                    AppRoutes.bookingShipmentConfirmationScreen,
                    extra: ShipmentConfirmationParams(
                      bookingProvider: bookingProvider,
                      homeProvider: widget.homeProvider,
                    ),
                  );
                }
              }
            },
            text: context.l10n.save,
          ),
        );
      },
    );
  }
}

/// Location tile
class _LocationTile extends StatelessWidget {
  /// Constructor
  const _LocationTile({
    required this.title,
    required this.subtitle,
    required this.trailing,
    required this.heading,
  });

  /// Custom title
  final String title;

  /// Custom subtitle
  final String subtitle;

  /// Custom trailing
  final String trailing;

  /// Custom heading icon widget
  final Widget heading;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: AppSize.w6,
      children: [
        heading,
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: AppSize.h1,
            children: [
              Row(
                children: [
                  Text(
                    title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: AppColors.ff343A40,
                    ),
                  ),
                  Flexible(
                    fit: FlexFit.tight,
                    child: Text(
                      trailing,
                      maxLines: 4,
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: AppColors.ff67509C,
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ],
              ),
              Text(
                subtitle,
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.ff6C757D,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
