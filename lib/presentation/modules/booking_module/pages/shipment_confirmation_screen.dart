import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/widgets/stock_vehicles_info_widget.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

/// Shipment confirmation page
class ShipmentConfirmationScreen extends StatefulWidget {
  /// Default constructor
  const ShipmentConfirmationScreen({
    super.key,
    required this.shipmentConfirmationParams,
  });
  final ShipmentConfirmationParams shipmentConfirmationParams;

  @override
  State<ShipmentConfirmationScreen> createState() =>
      _ShipmentConfirmationScreenState();
}

class _ShipmentConfirmationScreenState
    extends State<ShipmentConfirmationScreen> {
  @override
  void deactivate() {
    widget.shipmentConfirmationParams.bookingProvider.isShowLoader.value =
        false;
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    final homeProvider = widget.shipmentConfirmationParams.homeProvider;
    return PopScope(
      onPopInvokedWithResult: (didPop, result) => widget
          .shipmentConfirmationParams
          .bookingProvider
          .getPaymentToken
          ?.cancel(),
      child: Scaffold(
        backgroundColor: AppColors.ffF8F9FA,
        appBar: CustomAppBar(title: context.l10n.shipmentConfirmation),
        body: ValueListenableBuilder(
          valueListenable:
              widget.shipmentConfirmationParams.bookingProvider.isShowLoader,
          builder: (context, value, child) =>
              AppLoader(isShowLoader: value, child: child!),
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
            children: <Widget>[
              Container(
                padding: EdgeInsets.all(AppSize.h16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppSize.r8),
                ),
                child: Column(
                  spacing: AppSize.h16,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with "Your Shipment" and "Edit"
                    Text(
                      context.l10n.yourShipment,
                      style: context.textTheme.titleLarge,
                    ),

                    /// Number of total vehicles
                    NoOfVehicleWidget(
                      noOfVehicle: homeProvider.selectedVehicleInfo.length
                          .toString(),
                      titleSize: AppSize.sp16,
                      subTitleSize: AppSize.sp16,
                    ),

                    // Locations and dates row
                    Builder(
                      builder: (context) {
                        final startAddress = widget
                            .shipmentConfirmationParams
                            .homeProvider
                            .selectedOriginStockLocation
                            .value
                            ?.address;
                        final endAddress = widget
                            .shipmentConfirmationParams
                            .homeProvider
                            .selectedDropStockLocation
                            .value
                            ?.address;
                        return LocationInfoWidget(
                          /// user start and end location
                          startLatitude: startAddress?.latitude,
                          startLongitude: startAddress?.longitude,
                          endLatitude: endAddress?.latitude,
                          endLongitude: endAddress?.longitude,

                          /// date and place
                          startLocationTitle:
                              widget
                                  .shipmentConfirmationParams
                                  .homeProvider
                                  .selectedOriginStockLocation
                                  .value
                                  ?.name ??
                              widget.shipmentConfirmationParams
                                  .homeProvider
                                  .selectedOriginStockLocation
                                  .value
                                  ?.address
                                  ?.street ??
                                  '',
                          startLocationDate:
                              widget
                                  .shipmentConfirmationParams
                                  .homeProvider
                                  .pickupDate
                                  .value
                                  ?.monthDateFormate ??
                              '',
                          endLocationTitle:
                              widget
                                  .shipmentConfirmationParams
                                  .homeProvider
                                  .selectedDropStockLocation
                                  .value
                                  ?.name ??
                                  widget.shipmentConfirmationParams
                                      .homeProvider
                                      .selectedDropStockLocation
                                      .value
                                      ?.address
                                      ?.street ??
                                  '',
                          endLocationDate:
                              widget
                                  .shipmentConfirmationParams
                                  .homeProvider
                                  .deliveryDate
                                  .value
                                  ?.monthDateFormate ??
                              '',
                        );
                      },
                    ),
                  ],
                ),
              ),
              Gap(AppSize.h14),
              Column(
                children: List.generate(
                  widget
                      .shipmentConfirmationParams
                      .bookingProvider
                      .transporterDetailList
                      .value
                      .length,
                  (index) => Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h16),
                    child: StockVehiclesInfoWidget(
                      providerData: widget
                          .shipmentConfirmationParams
                          .bookingProvider
                          .transporterDetailList
                          .value[index],
                      mainIndex: index,
                      bookingProvider:
                          widget.shipmentConfirmationParams.bookingProvider,
                      homeProvider:
                          widget.shipmentConfirmationParams.homeProvider,
                      onClose: (){},
                    ),
                  ),
                ),
              ),
              // StockVehiclesInfoWidget(
              //   onClose: () {},
              //   vehicleList: const [],
              //   mainIndex: 0,
              //   controller: BookingProvider(),
              // ),
              Gap(AppSize.h14),
            ],
          ),
        ),
        bottomNavigationBar: ValueListenableBuilder(
          valueListenable: homeProvider.isShowLoader,
          builder: (context, isShowLoader, _) {
            return AppButton(
              text: context.l10n.proceedToPayment,
              onPressed: () {
                if (isShowLoader) return;
                widget.shipmentConfirmationParams.bookingProvider.getPayment(
                  context,
                  homeProvider: widget.shipmentConfirmationParams.homeProvider,
                );
              },
            );
          },
        ),
      ),
    );
  }
}
