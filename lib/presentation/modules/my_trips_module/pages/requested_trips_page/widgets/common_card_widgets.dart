import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/edit_rested_trip_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

/// Common Card Widgets for tab
class CommonCardWidgets extends StatelessWidget {
  /// Common Card Constructor
  const CommonCardWidgets({
    required this.requestedTripPageIndex,
    super.key,
    required this.index,
  });

  /// for button ui change
  final int requestedTripPageIndex;
  final int index;

  @override
  Widget build(BuildContext context) {
    final requestedTripsProvider = context.read<RequestedTripsProvider>();
    final requestedTripData = switch (requestedTripPageIndex) {
      0 => requestedTripsProvider.exclusiveTripList.value[index],
      1 => requestedTripsProvider.waitingTripList.value[index],
      _ => requestedTripsProvider.restedTripList.value[index],
    };
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(
          AppSize.sp16,
        ).subtract(EdgeInsets.only(bottom: AppSize.h10)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NoOfVehicleWidget(
              noOfVehicle:
                  requestedTripData.carDetails?.length.toString() ?? '',
              isTitleWidget: true,
            ),

            /// Locations and dates row
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h16),
              child: LocationInfoWidget(
                startLocationTitle:
                    requestedTripData.startStopLocation?.fullAddress ??
                    requestedTripData.userStartLocation?.street ??
                    '',
                startLatitude:
                    requestedTripData.startStopLocation?.address?.latitude ??
                    requestedTripData.userStartLocation?.latitude,
                startLongitude:
                    requestedTripData.startStopLocation?.address?.longitude ??
                    requestedTripData.userStartLocation?.longitude,
                endLatitude:
                    requestedTripData.endStopLocation?.address?.latitude ??
                    requestedTripData.userEndLocation?.latitude,
                endLongitude:
                    requestedTripData.endStopLocation?.address?.longitude ??
                    requestedTripData.userEndLocation?.longitude,
                startLocationDate:
                    requestedTripData.customerStartDate?.monthDateFormate ?? '',
                endLocationTitle:
                    requestedTripData.endStopLocation?.fullAddress ??
                    requestedTripData.userEndLocation?.street ??
                    '',
                endLocationDate:
                    requestedTripData.customerEndDate?.monthDateFormate ?? '',
              ),
            ),

            if (requestedTripPageIndex != 1)
              Divider(
                thickness: AppSize.h1,
                height: 0,
                color: AppColors.ffDEE2E6,
              ),
            if (requestedTripPageIndex != 1) Gap(AppSize.h3),
            if (requestedTripPageIndex != 1)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  /// The below condition is for the temporary
                  if (requestedTripPageIndex == 2)
                    TextButton(
                      onPressed: () {
                        if (requestedTripPageIndex == 0) {
                          // pause trip
                        } else {
                          AppNavigationService.pushNamed(
                            context,
                            AppRoutes.tripsEditRestedTripScreen,
                            extra: EditRestedTripParams(
                              tripModelData: requestedTripData,
                              isFromDetailScreen: false,
                            ),
                            //
                            // EditRestedTripScreen(data: requestedTripData),
                          );
                          //onResumeTripPressed,
                        }
                      },
                      child: Text(
                        requestedTripPageIndex == 0
                            ? context.l10n.pauseTrip
                            : context.l10n.resumeTrip,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: AppColors.ff67509C,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  TextButton(
                    onPressed: () => context.showAlertDialog(
                      defaultActionText: context.l10n.yes,
                      cancelActionText: context.l10n.no,
                      onCancelActionPressed: Navigator.pop,
                      onDefaultActionPressed: (dContext) {
                        Navigator.pop(dContext);
                        context.read<RequestedTripsProvider>().removeReqTrip(
                          index,
                          requestedTripData.id?.toString() ?? '0',
                          context,
                          requestedTripPageIndex: requestedTripPageIndex,
                        );
                      },
                      titleWidget: Text(
                        context.l10n.cancelTrip,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                          color: AppColors.ff6C757D,
                        ),
                      ),
                      contentWidget: Text(
                        context.l10n.areUSureCancel,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 15),
                      ),
                    ),
                    child: Text(
                      context.l10n.cancelTrip,
                      style: context.textTheme.bodyLarge?.copyWith(
                        color: AppColors.errorColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            if (requestedTripPageIndex == 1)
              Center(
                child: TextButton(
                  onPressed: () {},
                  child: Text(
                    context.l10n.restTrip,
                    style: context.textTheme.bodyLarge?.copyWith(
                      color: AppColors.errorColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
