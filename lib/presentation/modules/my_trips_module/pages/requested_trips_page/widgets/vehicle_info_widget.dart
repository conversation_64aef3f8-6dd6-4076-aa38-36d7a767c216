import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// VehiclesInfoWidget
class VehiclesInfoWidget extends StatefulWidget {
  /// Constructor
  const VehiclesInfoWidget({
    super.key,
    this.onClose,
    required this.carList,
    this.isExclusiveScreen = true,
  });

  final List<VehicleInfoModel> carList;

  /// On close button tap (optional, button will only show up if callback is not null)
  final VoidCallback? onClose;

  final bool isExclusiveScreen;

  @override
  State<VehiclesInfoWidget> createState() => _VehiclesInfoWidgetState();
}

class _VehiclesInfoWidgetState extends State<VehiclesInfoWidget> {
  late PageController _pageController;

  @override
  void initState() {
    _pageController = PageController();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
        color: AppColors.white,
      ),
      padding: widget.isExclusiveScreen ? EdgeInsets.all(AppSize.h16) : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        spacing: widget.isExclusiveScreen ? AppSize.h16 : AppSize.h4,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.vehicleInfo,
                style: widget.isExclusiveScreen
                    ? context.textTheme.titleLarge
                    : context.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
              ),
              AnimatedBuilder(
                animation: _pageController,
                builder: (context, child) {
                  return Text(
                    '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}'
                    '/${widget.carList.length}',
                    style: context.textTheme.bodyLarge?.copyWith(
                      fontSize: AppSize.sp16,
                      fontWeight: FontWeight.w400,
                    ),
                  );
                },
              ),
            ],
          ),
          SizedBox(
            height: AppSize.h160,
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.carList.length,
              itemBuilder: (context, index) {
                final carData = widget.carList[index];
                return Container(
                  padding: EdgeInsets.all(AppSize.h16),
                  margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.ffADB5BD),
                    borderRadius: BorderRadius.circular(AppSize.r5),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    spacing: AppSize.h8,
                    children: [
                      Row(
                        children: [
                          if (carData.status.isNotEmptyAndNotNull)
                            Flexible(
                              fit: FlexFit.tight,
                              child: Padding(
                                padding: EdgeInsets.only(right: AppSize.w8),
                                child: MarqueeWidget(
                                  child: Text(
                                    (carData.status ?? '').upToLower,
                                    style: context.textTheme.titleLarge
                                        ?.copyWith(
                                          color: AppColors.errorColor
                                              .withValues(alpha: 0.8),
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ),
                              ),
                            ),
                          GestureDetector(
                            onTap: () => AppNavigationService.pushNamed(
                              context,
                              AppRoutes.carInfoSheet,
                              extra: CarInfoParams(
                                carDetail: CarDetailModel(
                                  brandName:
                                      carData.car?.brand ?? carData.brand ?? '',
                                  // insurance: data.i,
                                  fromCarToBePickedUpLocation:
                                      carData.fromCarToBePickedUpLocation,
                                  serviceType: carData.serviceType,
                                  isCarPickedUp:
                                      carData.isCarPickedUpToStopLocation,
                                  charges: carData.charges,
                                  carName:
                                      carData.car?.model ?? carData.model ?? '',
                                  yearValue:
                                      carData.car?.year ??
                                      carData.year?.toString() ??
                                      '',
                                  serialNumber: carData.serialNumber ?? '',
                                  carSize: carData.car?.size ?? carData.size,
                                  carDescription: carData.carDescription,
                                  brand: '',
                                  car: null,
                                  images: carData.images,
                                  year:
                                      carData.year?.toString() ??
                                      carData.car?.year?.toString() ??
                                      '',
                                  isWinchRequired: carData.isWinchRequired,
                                ),
                              ),
                            ),
                            behavior: HitTestBehavior.opaque,
                            child: Text(
                              context.l10n.details,
                              style: context.textTheme.titleLarge?.copyWith(
                                color: AppColors.primaryColor,
                                fontSize: AppSize.sp14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if ((carData.brand ?? carData.car?.brand) != null)
                            VehiclesInfoField(
                              title: context.l10n.carBrand,
                              value: carData.brand ?? carData.car?.brand ?? '',
                            ),
                          if ((carData.model ?? carData.car?.model) != null)
                            VehiclesInfoField(
                              title: context.l10n.carModel,
                              value:
                                  (carData.model ?? carData.car?.model) ?? '',
                            ),
                        ],
                      ),
                      if ((carData.year ?? carData.car?.year) != null &&
                          (carData.model ?? carData.car?.model) != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (carData.serialNumber != null)
                              VehiclesInfoField(
                                title: '${context.l10n.carSerial} #',
                                value: carData.serialNumber ?? '',
                              ),
                            if ((carData.year ?? carData.car?.year) != null)
                              VehiclesInfoField(
                                title: context.l10n.carYear,
                                value:
                                    (carData.year ?? carData.car?.year)
                                        ?.toString() ??
                                    '',
                              ),
                          ],
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
          ),
          if (!widget.isExclusiveScreen) Gap(AppSize.h1),
          AnimatedBuilder(
            animation: _pageController,
            builder: (context, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (var i = 0; i < widget.carList.length; i++)
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                      width: AppSize.w8,
                      height: AppSize.w8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == (_pageController.page?.round() ?? 0)
                            ? Colors.blue
                            : Colors.grey[400],
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
