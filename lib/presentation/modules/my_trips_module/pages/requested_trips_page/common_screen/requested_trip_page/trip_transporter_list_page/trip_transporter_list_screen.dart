import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/trip_shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/widgets/show_filter_sheet.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/models/trip_transporter_list_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/provider/trip_transporter_list_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/widgets/trip_cart_sheet.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/assign_car_widget.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/title_info.dart';
import 'package:transport_match/widgets/transporter_card.dart';

/// Transporter listing UI
class TripTransporterListScreen extends StatelessWidget {
  /// Constructor
  const TripTransporterListScreen({
    super.key,
    required this.tripTransporterListParams,
  });
  final TripTransporterListParams tripTransporterListParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TripTransporterListProvider(
        context,
        tripTransporterListParams.tripData,
      ),
      child: Builder(
        builder: (context) {
          final tripTransporterListProvider = context
              .read<TripTransporterListProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            bottomNavigationBar:
                Selector<TripTransporterListProvider, (int, TripModel?)>(
                  selector: (p0, tripTransporterListProvider) => (
                    tripTransporterListProvider.totalAssignCar,
                    tripTransporterListProvider.selectedTrip,
                  ),
                  builder: (context, providerData, child) {
                    return providerData.$2 == null
                        ? const SizedBox.shrink()
                        : AppButton(
                            text: context.l10n.continues,
                            buttonColor: providerData.$1 == 0
                                ? AppColors.unSelectedColor
                                : null,
                            onPressed: () {
                              final tripTransporterListProvider = context
                                  .read<TripTransporterListProvider>();
                              if (providerData.$1 != 0) {
                                AppNavigationService.pushNamed(
                                  context,
                                  AppRoutes.tripsTripShipmentConfirmationScreen,
                                  extra: TripShipmentConfirmationParams(
                                    bookingSessionId:
                                        tripTransporterListProvider
                                            .bookingSessionId ??
                                        '',
                                    transporterDetailList:
                                        tripTransporterListProvider
                                            .transporterDetailList
                                            .value,
                                    providerList: tripTransporterListProvider
                                        .providerList
                                        .value,
                                    selectedTrip: tripTransporterListProvider
                                        .selectedTrip,
                                  ),
                                );
                              }
                            },
                          );
                  },
                ),
            appBar: CustomAppBar(title: context.l10n.transportList),
            body: ValueListenableBuilder(
              valueListenable: tripTransporterListProvider.isFindShowLoader,
              builder: (context, isFindShowLoader, mainChild) => AppLoader(
                isShowLoader: isFindShowLoader,
                child: ValueListenableBuilder(
                  valueListenable:
                      tripTransporterListProvider.findTransporterNextUrl,
                  builder: (contexts, findTransporterNextUrl, child) {
                    final tripTransporterListProvider = context
                        .read<TripTransporterListProvider>();
                    return Selector<TripTransporterListProvider, TripModel?>(
                      selector: (p0, tripTransporterListProvider) =>
                          tripTransporterListProvider.selectedTrip,
                      builder: (context, selectedTrip, child) {
                        return SmartRefresher(
                          controller:
                              tripTransporterListProvider.refresherController,
                          enablePullUp:
                              findTransporterNextUrl.isNotEmptyAndNotNull,
                          onRefresh: () async => tripTransporterListProvider
                              .findTransporter(
                                context,
                                tripData:
                                    tripTransporterListProvider.selectedTrip!,
                                isFilter: true,
                              )
                              .whenComplete(
                                tripTransporterListProvider
                                    .refresherController
                                    .refreshCompleted,
                              ),
                          onLoading: () async => tripTransporterListProvider
                              .findTransporter(
                                context,
                                tripData:
                                    tripTransporterListProvider.selectedTrip!,
                                isFilter: true,
                                isPagination: true,
                                isWantShowLoader: false,
                              )
                              .whenComplete(
                                tripTransporterListProvider
                                    .refresherController
                                    .loadComplete,
                              ),
                          child: selectedTrip == null
                              ? isFindShowLoader
                                    ? const SizedBox.shrink()
                                    : Center(
                                        child: Text(
                                          context.l10n.noProviderFound,
                                        ),
                                      )
                              : ListView(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w16,
                                  ),
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(AppSize.h16),
                                      margin: EdgeInsets.only(
                                        bottom: AppSize.h16,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        spacing: AppSize.h16,
                                        children: [
                                          Text(
                                            context.l10n.yourShipment,
                                            style: context.textTheme.titleLarge,
                                          ),
                                          // Number of total vehicles
                                          TitleInfo(
                                            title:
                                                context.l10n.noOfTotalVehicle,
                                            subTitle:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.carDetails
                                                    ?.length
                                                    .toString() ??
                                                '',
                                            subTitleFontWeight: FontWeight.w600,
                                          ),

                                          /// Locations and dates row
                                          LocationInfoWidget(
                                            /// user start and end location
                                            startLatitude:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userStartLocation
                                                    ?.latitude,
                                            startLongitude:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userStartLocation
                                                    ?.longitude,
                                            endLatitude:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userEndLocation
                                                    ?.latitude,
                                            endLongitude:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userEndLocation
                                                    ?.longitude,
                                            startLocationTitle:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userStartLocation
                                                    ?.city ??
                                                '',
                                            startLocationDate:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.customerStartDate
                                                    ?.monthDateFormate ??
                                                '',
                                            endLocationTitle:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.userEndLocation
                                                    ?.city ??
                                                '',
                                            endLocationDate:
                                                tripTransporterListProvider
                                                    .selectedTrip
                                                    ?.customerEndDate
                                                    ?.monthDateFormate ??
                                                '',
                                          ),
                                        ],
                                      ),
                                    ),
                                    Selector<TripTransporterListProvider, int>(
                                      selector:
                                          (p0, tripTransporterListProvider) =>
                                              tripTransporterListProvider
                                                  .totalAssignCar,
                                      builder:
                                          (context, totalAssignCar, child) {
                                            return AssignCarWidget(
                                              value: totalAssignCar,
                                              totalCar:
                                                  tripTransporterListProvider
                                                      .selectedTrip
                                                      ?.carDetails
                                                      ?.length ??
                                                  0,
                                            );
                                          },
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          context.l10n.chooseTransporter,
                                          style: context.textTheme.titleLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                fontSize: AppSize.sp20,
                                              ),
                                        ),
                                        GestureDetector(
                                          behavior: HitTestBehavior.opaque,
                                          onTap: () => showModalBottomSheet(
                                            context: context,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                    top: Radius.circular(
                                                      AppSize.r12,
                                                    ),
                                                  ),
                                            ),
                                            backgroundColor:
                                                AppColors.pageBGColor,
                                            isScrollControlled: true,
                                            builder: (context) {
                                              double requiredSlot = 0;
                                              for (final element
                                                  in tripTransporterListProvider
                                                          .selectedTrip
                                                          ?.carDetails ??
                                                      <VehicleInfoModel>[]) {
                                                requiredSlot +=
                                                    element.car?.size ?? 0;
                                              }

                                              return FilterScreen(
                                                requiredSlot: requiredSlot + 2,
                                                onSave:
                                                    (
                                                      rate, {
                                                      required isFromLow,
                                                      required isIn2Day,
                                                      team,
                                                      required isWinch,
                                                      required isException,
                                                    }) {
                                                      tripTransporterListProvider
                                                          .changeFilterRate(
                                                            rate,
                                                          );
                                                      tripTransporterListProvider
                                                              .team
                                                              .value =
                                                          team ?? 0;
                                                      tripTransporterListProvider
                                                              .isLowest
                                                              .value =
                                                          isFromLow;
                                                      tripTransporterListProvider
                                                              .is2Days
                                                              .value =
                                                          isIn2Day;
                                                      tripTransporterListProvider
                                                        ..isWinch.value =
                                                            isWinch
                                                        ..notify()
                                                        ..isException.value =
                                                            isException
                                                        ..notify();
                                                      AppNavigationService.pop(
                                                        context,
                                                      );
                                                      tripTransporterListProvider
                                                          .findTransporter(
                                                            context,
                                                            tripData:
                                                                tripTransporterListProvider
                                                                    .selectedTrip!,
                                                            isFilter: true,
                                                            isWinch: isWinch,
                                                            isException:
                                                                isException,
                                                          );
                                                    },
                                                onClear: () {
                                                  AppNavigationService.pop(
                                                    context,
                                                  );
                                                  tripTransporterListProvider
                                                    ..clearFilter()
                                                    ..findTransporter(
                                                      context,
                                                      tripData:
                                                          tripTransporterListProvider
                                                              .selectedTrip!,
                                                      isFilter: true,
                                                    );
                                                },
                                                isWinch:
                                                    tripTransporterListProvider
                                                        .isWinch
                                                        .value,
                                                is2Day:
                                                    tripTransporterListProvider
                                                        .is2Days
                                                        .value,
                                                isLow:
                                                    tripTransporterListProvider
                                                        .isLowest
                                                        .value,
                                                rate:
                                                    tripTransporterListProvider
                                                        .rate
                                                        .value,
                                                team:
                                                    tripTransporterListProvider
                                                        .team
                                                        .value,
                                                isTripFiler: true,
                                                isWinchFixed:
                                                    tripTransporterListProvider
                                                        .isWinchFix
                                                        .value,
                                                isException:
                                                    tripTransporterListProvider
                                                        .isException
                                                        .value,
                                                isExceptionFixed:
                                                    tripTransporterListProvider
                                                        .isExceptionFix
                                                        .value,
                                              );
                                            },
                                          ),
                                          child: Text(
                                            context.l10n.filterBy,
                                            style: context.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: AppSize.sp16,
                                                  color: AppColors.primaryColor,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                      child: ValueListenableBuilder(
                                        valueListenable:
                                            tripTransporterListProvider
                                                .providerList,
                                        builder: (contexts, list, child) {
                                          return ValueListenableBuilder(
                                            valueListenable:
                                                tripTransporterListProvider
                                                    .isFindShowLoader,
                                            builder: (context, isFindShowLoader, child) {
                                              return list.isEmpty &&
                                                      !isFindShowLoader
                                                  ? SizedBox(
                                                      height: AppSize.h200,
                                                      child: Center(
                                                        child: Text(
                                                          context
                                                              .l10n
                                                              .noProviderFound,
                                                        ),
                                                      ),
                                                    )
                                                  : Consumer<
                                                      TripTransporterListProvider
                                                    >(
                                                      builder: (contexts, controller, child) {
                                                        return ListView.builder(
                                                          itemCount:
                                                              list.length,
                                                          shrinkWrap: true,
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          padding:
                                                              EdgeInsets.zero,
                                                          itemBuilder: (contexts, index) {
                                                            final data =
                                                                list[index];
                                                            final isSelected = controller
                                                                .transporterDetailList
                                                                .value
                                                                .any(
                                                                  (e) =>
                                                                      e.trip ==
                                                                      data.id,
                                                                );

                                                            final isProviderDisabled =
                                                                tripTransporterListProvider
                                                                    .disableProvider(
                                                                      context,
                                                                      data,
                                                                      isSelected:
                                                                          isSelected,
                                                                    );

                                                            tripTransporterListProvider
                                                                    .assignCarValue =
                                                                isSelected
                                                                ? tripTransporterListProvider
                                                                          .transporterDetailList
                                                                          .value
                                                                          .where(
                                                                            (
                                                                              e,
                                                                            ) =>
                                                                                e.trip ==
                                                                                data.id,
                                                                          )
                                                                          .firstOrNull
                                                                          ?.carDetails
                                                                          .length ??
                                                                      0
                                                                : 1;

                                                            return GestureDetector(
                                                              behavior:
                                                                  HitTestBehavior
                                                                      .opaque,
                                                              onTap: () =>
                                                                  openTripCartSheet(
                                                                    contexts,
                                                                    data,
                                                                    tripTransporterListProvider,
                                                                    isTrip:
                                                                        true,
                                                                  ),
                                                              child: TransporterCard(
                                                                data: data,
                                                                isSelected:
                                                                    isSelected,
                                                                isTrip: true,
                                                                isDisabled:
                                                                    isProviderDisabled,
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                    );
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
