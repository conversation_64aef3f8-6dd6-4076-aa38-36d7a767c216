import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/provider/requested_trip_common_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/models/trip_transporter_list_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/card_row.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/vehicle_info_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

///Requested Trips page
class RequestedTripsScreen extends StatelessWidget {
  /// Requested Trips Constructor
  const RequestedTripsScreen({required this.requestedTripsParams, super.key});

  final RequestedTripsParams requestedTripsParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) =>
          RequestedTripCommonProvider(requestedTripsParams.tripId, context),
      builder: (context, child) {
        final requestedTripCommonProvider = context
            .read<RequestedTripCommonProvider>();
        return Scaffold(
          backgroundColor: AppColors.ffF8F9FA,
          appBar: CustomAppBar(title: context.l10n.requestedTrips),
          body: Selector<RequestedTripCommonProvider, (bool, TripModel?)>(
            selector: (p0, requestedTripsProvider) => (
              requestedTripsProvider.isShowTripDetailLoader,
              requestedTripsProvider.tripData,
            ),
            builder: (context, loaderData, child) {
              final data = loaderData.$2;
              return AppLoader(
                isShowLoader: loaderData.$1,
                child: loaderData.$1
                    ? const SizedBox.expand()
                    : EasyRefresh(
                        controller:
                            requestedTripCommonProvider.refreshController,
                        onRefresh: () async =>
                            requestedTripCommonProvider.getTripDetails(
                              requestedTripsParams.tripId,
                              context,
                            ),
                        child: (!loaderData.$1 && data == null)
                            ? Center(child: Text(context.l10n.noTripFound))
                            : SingleChildScrollView(
                                padding: EdgeInsets.all(AppSize.appPadding),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  spacing: AppSize.h10,
                                  children: [
                                    Text(
                                      context.l10n.userLocation,
                                      style: context.textTheme.titleLarge,
                                    ),
                                    AppTextFormField(
                                      fillColor: AppColors.ffE6E6E6,
                                      borderSide: const BorderSide(
                                        color: AppColors.unSelectedColor,
                                      ),
                                      hintText:
                                          data?.userStartLocation?.street ??
                                          data?.startStopLocation?.fullAddress,
                                      readOnly: true,
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        vertical: AppSize.h6,
                                      ),
                                      child: AppTextFormField(
                                        fillColor: AppColors.ffE6E6E6,
                                        borderSide: const BorderSide(
                                          color: AppColors.unSelectedColor,
                                        ),
                                        hintText:
                                            data?.userEndLocation?.street ??
                                            data?.endStopLocation?.fullAddress,
                                        readOnly: true,
                                      ),
                                    ),
                                    if (data?.carDetails != null &&
                                        (data?.carDetails?.isNotEmpty ?? false))
                                      Padding(
                                        padding: EdgeInsets.only(
                                          bottom: AppSize.h6,
                                        ),
                                        child: VehiclesInfoWidget(
                                          carList: data?.carDetails ?? [],
                                        ),
                                      ),
                                    if (requestedTripsParams.isWaitingList)
                                      CardRow(
                                        dateColor: AppColors.black,
                                        titleColor: AppColors.black,
                                        titleSize: AppSize.sp20,
                                        fontWeight: FontWeight.normal,
                                        title: context.l10n.towingCost,

                                        /// TODO: that date or amount both are in same
                                        date: r'$120',
                                      ),
                                    CardRow(
                                      icon: AppAssets.iconsLocationOrigin.image(
                                        height: AppSize.h24,
                                      ),
                                      title: context.l10n.pickupDate,
                                      date:
                                          data
                                              ?.customerStartDate
                                              ?.monthDateFormate ??
                                          '',
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                        bottom: AppSize.h4,
                                      ),
                                      child: CardRow(
                                        icon: AppAssets.iconsLocation.image(
                                          height: AppSize.h24,
                                        ),
                                        title: context.l10n.deliveryDate,
                                        date:
                                            data
                                                ?.customerEndDate
                                                ?.monthDateFormate ??
                                            '',
                                      ),
                                    ),
                                    Gap(AppSize.h10),
                                  ],
                                ),
                              ),
                      ),
              );
            },
          ),
          bottomNavigationBar:
              Selector<RequestedTripCommonProvider, (bool, TripModel?)>(
                selector: (p0, requestedTripsProvider) => (
                  requestedTripsProvider.isShowTripDetailLoader,
                  requestedTripsProvider.tripData,
                ),
                builder: (context, loaderData, child) {
                  return loaderData.$2 == null
                      ? const SizedBox.shrink()
                      : AppButton(
                          text: context.l10n.findTransporter,
                          onPressed: () {
                            if (!loaderData.$1) {
                              AppNavigationService.pushNamed(
                                context,
                                AppRoutes.tripsTripTransporterListScreen,
                                extra: TripTransporterListParams(
                                  tripData: loaderData.$2,
                                ),
                                //
                                // TripTransporterListPage(requestedTripsProvider: requestedTripsProvider),
                              );
                            }
                            //   AppNavigationService.pushNamed(
                            //     context,
                            //     const WaitingTransporterListScreen(),
                            //   );
                            // } else if (widget.value == 0) {
                            // requestedTripsProvider
                            //   ..clearFilter()
                            //   ..findTransporter(
                            //     context,
                            //     trip: data!,
                            //     isWinch: data.carDetails?.length == 1 &&
                            //         (data.carDetails?.any(
                            //               (element) => element.isWinchRequired,
                            //             ) ??
                            //             false),
                            //   );
                          },
                        );
                },
              ),
        );
      },
    );
  }
}
