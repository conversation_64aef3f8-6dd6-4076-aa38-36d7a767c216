import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/common_card_widgets.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

///Waiting List Tab ui
class WaitingListTab extends StatefulWidget {
  ///Constructor
  const WaitingListTab(this.requestedTripsProvider, {super.key});
  final RequestedTripsProvider requestedTripsProvider;

  @override
  State<WaitingListTab> createState() => _WaitingListTabState();
}

class _WaitingListTabState extends State<WaitingListTab> {
  final refreshController = RefreshController();
  @override
  void initState() {
    widget.requestedTripsProvider.getRequestedTripList(
      1,
      isWantShowLoader: true,
    );
    super.initState();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.requestedTripsProvider.waitingTripList,
      builder: (context, list, child) {
        return ValueListenableBuilder(
          valueListenable: widget.requestedTripsProvider.nextWaitingUrl,
          builder: (context, nextUrl, nextUrlChild) {
            return ValueListenableBuilder(
              valueListenable:
                  widget.requestedTripsProvider.isShowWaitingLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: SmartRefresher(
                    controller: refreshController,
                    enablePullUp: !isShowLoader && nextUrl.isNotEmptyAndNotNull,
                    onRefresh: () => widget.requestedTripsProvider
                        .getRequestedTripList(1)
                        .whenComplete(refreshController.refreshCompleted),
                    onLoading: () => widget.requestedTripsProvider
                        .getRequestedTripList(1, isPagination: true)
                        .whenComplete(refreshController.loadComplete),
                    child: !isShowLoader && list.isEmpty
                        ? const NoTripWidget()
                        : nextUrlChild!,
                  ),
                );
              },
            );
          },
          child: SingleChildScrollView(
            child: Column(
              children: List.generate(
                list.length,
                (index) => GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () =>
                      AppNavigationService.pushNamed<RequestedTripsProvider>(
                        context,
                        AppRoutes.tripsRequestedTripsScreen,
                        extra: RequestedTripsParams(
                          tripId: list[index].id?.toString() ?? '0',
                          isWaitingList: true,
                        ),
                        //
                        // RequestedTripsScreen(
                        //   value: 1,
                        //   id: list[index].id?.toString() ?? '',
                        //   requestedTripsProvider: widget.requestedTripsProvider,
                        // ),
                        // controller: widget.requestedTripsProvider,
                      ),
                  // widget.requestedTripsProvider.getTripDetails(
                  //   list[index].id ?? 0,
                  //   context,
                  //   value: 1,
                  //   index: index,
                  //   requestedTripsProvider: widget.requestedTripsProvider,
                  // );
                  //   AppNavigationService.pushNamed(
                  //     context,
                  //     ChangeNotifierProvider.value(
                  //       value: widget.requestedTripsProvider,
                  //       child: RequestedTripsScreen(
                  //         value: 1,
                  //         data: list[index],
                  //       ),
                  //     ),
                  //   );
                  // },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSize.w8,
                      vertical: AppSize.h8,
                    ),
                    child: CommonCardWidgets(
                      requestedTripPageIndex: 1,
                      index: index,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
