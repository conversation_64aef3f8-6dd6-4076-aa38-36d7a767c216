import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/pickup_delivery_date_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/pickup_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/stop_end_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/user_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_selection_list_widget.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/edit_rested_trip_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/widgets/added_rejected_info_widgets.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Edit Trip Page
class EditRestedTripScreen extends StatelessWidget {
  ///Constructor
  const EditRestedTripScreen({super.key, required this.editRestedTripParams});

  /// for Rested Requested page ui
  final EditRestedTripParams editRestedTripParams;

  @override
  Widget build(BuildContext context) {
    final isExclusive =
        editRestedTripParams.tripModelData.bookingType ==
        BookingType.EXCLUSIVE.name;
    return Form(
      child: ChangeNotifierProvider(
        create: (context) => HomeProvider(
          data: editRestedTripParams.tripModelData,
          isExclusive: isExclusive,
        ),
        child: Consumer<HomeProvider>(
          builder: (context, homeProvider, _) {
            return Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              appBar: CustomAppBar(title: context.l10n.editTrip),
              body: ValueListenableBuilder(
                valueListenable: homeProvider.isAssignDataLoad,
                builder: (context, value, mainChild) {
                  return ValueListenableBuilder(
                    valueListenable: homeProvider.isShowLoader,
                    builder: (context, homeLoader, child) {
                      return AppLoader(
                        isShowLoader: value || homeLoader,
                        child: mainChild!,
                      );
                    },
                  );
                },
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if(isExclusive)...[
                        Gap(AppSize.h16),
                        Text(
                          context.l10n.userLocation,
                          style: context.textTheme.titleLarge,
                          textAlign: TextAlign.left,
                        ),
                        Gap(AppSize.h12),
                        UserLocationWidget(homeProvider: homeProvider)
                      ],

                      /// start and end stop location selection widget
                      if (!isExclusive)
                        const StopEndLocationWidget()
                      else
                        Gap(AppSize.h16),

                      /// vehicle selection widget
                      const VehicleSelectionListWidget(),

                      /// added and rejected widget
                      AddedRejectedInfoWidgets(
                        data:
                            editRestedTripParams.tripModelData.carDetails
                                ?.where(
                                  (element) =>
                                      element.verificationStatus ==
                                      CarVerificationStatus.REJECTED.name,
                                )
                                .toList() ??
                            [],
                        isRejected: true,
                      ),

                      /// user pickup location for stop location widget
                      const PickupLocationWidget(),

                      /// pickup and delivery date picker widget
                      const PickupDeliveryDateWidget(),
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSize.sp16),
                        child: AppConfirmCheckBox(
                          description:
                              context.l10n.transportAllVehicleInOneTruck,
                          value: homeProvider.isInAllVehicle.value,
                          onSelectionChanged: ({required bool value}) {
                            homeProvider
                              ..isInAllVehicle.value = value
                              ..notify();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottomNavigationBar: ValueListenableBuilder(
                valueListenable: homeProvider.isAssignDataLoad,
                builder: (context, value, mainChild) {
                  return ValueListenableBuilder(
                    valueListenable: homeProvider.isShowLoader,
                    builder: (context, homeLoader, child) {
                      return value || homeLoader
                          ? const SizedBox()
                          : mainChild!;
                    },
                  );
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSize.w16,
                  ).add(EdgeInsets.only(bottom: AppSize.h32, top: AppSize.h5)),
                  child: Row(
                    spacing: AppSize.w10,
                    children: [
                      Flexible(
                        child: AppButton(
                          text: context.l10n.save,
                          isBottomBtn: false,
                          buttonColor: AppColors.white,
                          borderColor: AppColors.ff0087C7,
                          isFillButton: false,
                          onPressed: () => homeProvider.updateRestedTrip(
                            context,
                            bookingId:
                                editRestedTripParams.tripModelData.id
                                    ?.toString() ??
                                '',
                            isExclusive: isExclusive,
                          ),
                        ),
                      ),
                      if (!homeProvider.selectedVehicleInfo.any(
                        (e) => e.showManualInputs,
                      ))
                        Flexible(
                          child: AppButton(
                            text: isExclusive
                                ? context.l10n.sendRequest
                                : context.l10n.findTransporter,
                            isBottomBtn: false,
                            onPressed: () async {
                              if (Form.of(context).validate()) {
                                if (isExclusive) {
                                  await homeProvider.bookExclusive(
                                    context,
                                    homeProvider,
                                    isFromRestedDetailScreen:
                                        editRestedTripParams.isFromDetailScreen,
                                    isRested: true,
                                  );
                                } else {
                                  await homeProvider.findTransporter(
                                    context,
                                    homeProvider,
                                    isRested: true,
                                  );
                                }
                              }
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
