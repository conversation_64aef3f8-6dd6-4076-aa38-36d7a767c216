import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/upcoming_tab_screen.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

/// Edit Transporter card
class TripTransporterCard extends StatelessWidget {
  const TripTransporterCard({super.key, required this.data});

  /// Constructor
  final TripModel data;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h16),
      padding: EdgeInsets.all(AppSize.h16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSize.h16,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NoOfVehicleWidget(
                noOfVehicle: data.noOfCars?.toString() ?? '-',
                isTitleWidget: true,
              ),
/*
              Text.rich(
                TextSpan(
                  text:
                  '${data.totalTripDistance?.toStringAsFixed(1).smartFormat()}',
                  children: [
                    TextSpan(
                      text: '/Car',
                      style: context.textTheme.titleLarge?.copyWith(
                        color: AppColors.ffADB5BD,
                        fontSize: AppSize.sp12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                style: context.textTheme.bodyLarge?.copyWith(
                  color: AppColors.ff67509C,
                  fontSize: AppSize.sp18,
                  fontWeight: FontWeight.bold,
                ),
              ),
*/
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TitleInfo(
                title: context.l10n.transporter,
                subTitle: data.bookingDetails?.length == 1 ?data.bookingDetails?.first.tripData?.companyName??'-' : (data.bookingDetails??[]).isEmpty?'-': '${data.bookingDetails?.length} ${context.l10n.transporter}',
              ),
              Flexible(
                child: TitleInfo(
                  title: context.l10n.total_trip_cost,
                  subTitle: '${data.totalTripDistance}'.smartFormat(),
                  subTitleColor: AppColors.ff67509C,
                  subTitleFontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          /// Locations row
          LocationInfoWidget(
            /// latitude and longitude
            startLatitude:
                data.startStopLocation?.address?.latitude ??
                data.userStartLocation?.latitude,
            startLongitude:
                data.startStopLocation?.address?.longitude ??
                data.userStartLocation?.longitude,
            endLatitude:
                data.endStopLocation?.address?.latitude ??
                data.userEndLocation?.latitude,
            endLongitude:
                data.endStopLocation?.address?.longitude ??
                data.userEndLocation?.longitude,

            /// title and date
            startLocationTitle:
                data.startStopLocation?.fullAddress ??
                data.userStartLocation?.street ??
                '',
            startLocationDate:
                booking(data.bookingDetails ?? [], isStart: true)
                    ?.intermediateStartStopLocation
                    ?.estimatedArrivalDate
                    ?.monthDateFormate ??
                data.customerStartDate?.monthDateFormate ??
                '',
            endLocationTitle:
                data.endStopLocation?.fullAddress ??
                data.userEndLocation?.street ??
                '',
            endLocationDate:
                booking(data.bookingDetails ?? [])
                    ?.intermediateEndStopLocation
                    ?.estimatedArrivalDate
                    ?.monthDateFormate ??
                data.customerEndDate?.monthDateFormate ??
                '',
            centerWidget: (child) => Stack(
              children: [
                SizedBox(height: AppSize.h25, child: child),
                Positioned(
                  right: AppSize.r5,
                  left: AppSize.r5,
                  bottom: AppSize.r5,
                  child: AppAssets.iconsCar.image(
                    height: AppSize.h18,
                    fit: BoxFit.fitHeight,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
