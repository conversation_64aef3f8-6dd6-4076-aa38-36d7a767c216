import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/widgets/upcoming_common_card.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/provider/accepted_trip_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

/// Upcoming Tab screen
class UpcomingTabScreen extends StatefulWidget {
  /// Constructor
  const UpcomingTabScreen({super.key, required this.acceptedTripProvider});
  final AcceptedTripProvider acceptedTripProvider;

  @override
  State<UpcomingTabScreen> createState() => _UpcomingTabScreenState();
}

class _UpcomingTabScreenState extends State<UpcomingTabScreen> {
  final refreshController = RefreshController();
  @override
  void initState() {
    widget.acceptedTripProvider.getAcceptedTripList(0, isWantShowLoader: true);
    super.initState();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.acceptedTripProvider.upcomingTripList,
      builder: (context, list, child) {
        return ValueListenableBuilder(
          valueListenable: widget.acceptedTripProvider.nextUpcomingUrl,
          builder: (context, nextUrl, nextUrlChild) {
            return ValueListenableBuilder(
              valueListenable: widget.acceptedTripProvider.upcomingTripLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: SmartRefresher(
                    controller: refreshController,
                    enablePullUp: !isShowLoader && nextUrl.isNotEmptyAndNotNull,
                    onRefresh: () => widget.acceptedTripProvider
                        .getAcceptedTripList(0)
                        .whenComplete(refreshController.refreshCompleted),
                    onLoading: () => widget.acceptedTripProvider
                        .getAcceptedTripList(0, isPagination: true)
                        .whenComplete(refreshController.loadComplete),
                    child: !isShowLoader && list.isEmpty
                        ? const NoTripWidget()
                        : nextUrlChild!,
                  ),
                );
              },
            );
          },
          child: SingleChildScrollView(
            child: Column(
              children: List.generate(list.length, (index) {
                final data = list[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: AppSize.h16),
                  child: UpcomingCommonCard(
                    tripData: data,
                    onBack: (note, {isRemove = false}) {
                      widget.acceptedTripProvider
                        ..upcomingTripList
                            .value[index]
                            .bookingDetails
                            ?.firstOrNull
                            ?.notes = isRemove
                            ? []
                            : note != null
                            ? [note]
                            : []
                        ..notify();
                      setState(() {});
                    },
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}

SharedBooking? booking(List<BookingDetail> list, {bool isStart = false}) {
  int? index;
  if (list.isNotEmpty) {
    var date =
        (isStart
            ? list
                      .first
                      .sharedBookings
                      ?.firstOrNull
                      ?.intermediateStartStopLocation
                      ?.estimatedArrivalDate ??
                  list.first.tripData?.tripStartDate
            : list
                      .first
                      .sharedBookings
                      ?.firstOrNull
                      ?.intermediateEndStopLocation
                      ?.estimatedArrivalDate ??
                  list.first.tripData?.tripEndDate) ??
        DateTime.now();
    for (var i = 0; i < list.length; i++) {
      final newDate = isStart
          ? list[i]
                    .sharedBookings
                    ?.firstOrNull
                    ?.intermediateStartStopLocation
                    ?.estimatedArrivalDate ??
                list[i].tripData?.tripStartDate
          : list[i]
                    .sharedBookings
                    ?.firstOrNull
                    ?.intermediateEndStopLocation
                    ?.estimatedArrivalDate ??
                list[i].tripData?.tripEndDate;
      if (newDate != null) {
        if ((newDate.isAfter(date) || newDate.isSameDay(date)) && !isStart) {
          date = newDate;
          index = i;
        }
        if ((newDate.isBefore(date) || newDate.isSameDay(date)) && isStart) {
          date = newDate;
          index = i;
        }
      }
    }
  }
  return index != null ? list[index].sharedBookings?.firstOrNull : null;
}
