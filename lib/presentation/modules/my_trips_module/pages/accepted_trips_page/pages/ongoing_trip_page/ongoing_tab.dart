import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/widgets/ongoing_transporter_card.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/provider/accepted_trip_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

/// Ongoing Tab Ui
class OngoingTab extends StatefulWidget {
  /// Constructor
  const OngoingTab({super.key, required this.acceptedTripProvider});
  final AcceptedTripProvider acceptedTripProvider;

  @override
  State<OngoingTab> createState() => _OngoingTabState();
}

class _OngoingTabState extends State<OngoingTab> {
  final refreshController = EasyRefreshController();
  @override
  void initState() {
    widget.acceptedTripProvider.getAcceptedTripList(1, isWantShowLoader: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.acceptedTripProvider.ongoingTripList,
      builder: (context, list, child) {
        return ValueListenableBuilder(
          valueListenable: widget.acceptedTripProvider.nextOngoingUrl,
          builder: (context, nextUrl, nextUrlChild) {
            return ValueListenableBuilder(
              valueListenable: widget.acceptedTripProvider.ongoingTripLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: EasyRefresh(
                    controller: refreshController,
                    onRefresh: () async =>
                        widget.acceptedTripProvider.getAcceptedTripList(1),
                    onLoad: () async => nextUrl.isNotEmptyAndNotNull
                        ? widget.acceptedTripProvider.getAcceptedTripList(
                            1,
                            isPagination: true,
                          )
                        : null,
                    child: !isShowLoader && list.isEmpty
                        ? const NoTripWidget()
                        : nextUrlChild!,
                  ),
                );
              },
            );
          },
          child: SingleChildScrollView(
            child: Column(
              children: List.generate(list.length, (index) {
                final data = list[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: AppSize.h16),
                  child: OngoingTransporterCard(data: data),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
