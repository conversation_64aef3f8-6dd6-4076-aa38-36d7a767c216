import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/provider/chat_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/widgets/chat_bubble.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Chat Screen UI
class ChatScreen extends StatelessWidget {
  /// Constructor
  const ChatScreen({super.key, required this.chatParams});

  final ChatParams chatParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ChatProvider(
        receiverId: chatParams.receiverId,
        bookingDetailId: chatParams.bookingDetailId,
        // chatType: chatType,
        customerChatRoomParameter: chatParams.customerChatRoomParameter,
      ),
      child: Scaffold(
        backgroundColor: AppColors.pageBGColor,
        appBar: CustomAppBar(title: chatParams.title),
        body: AppPadding.symmetric(
          horizontal: AppSize.appPadding,
          child: Consumer<ChatProvider>(
            builder: (context, chatProvider, _) {
              final groupedMessages = chatProvider.groupedMessages.entries
                  .toList();
              return AppLoader(
                isShowLoader: chatProvider.isLoading,
                isFullOpacity: true,
                child: Column(
                  children: [
                    Expanded(
                      child: chatProvider.messages.isEmpty
                          ? !chatProvider.isLoading
                                ? Center(
                                    child: Text(
                                      context.l10n.noMessageYet,
                                      style: context.textTheme.bodyLarge!
                                          .copyWith(color: AppColors.ff6C757D),
                                    ),
                                  )
                                : const SizedBox.shrink()
                          : SmartRefresher(
                              controller: chatProvider.refreshController,
                              enablePullUp:
                                  chatProvider.nextUrl.isNotEmptyAndNotNull,
                              enablePullDown: false,
                              onLoading: () {
                                if (chatParams.customerChatRoomParameter !=
                                    null) {
                                  chatProvider
                                      .getOldChatMessages(
                                        chatParams.customerChatRoomParameter!.id
                                            .toString(),
                                      )
                                      .whenComplete(
                                        () => chatProvider.refreshController
                                            .loadComplete(),
                                      );
                                } else {
                                  chatProvider.refreshController.loadComplete();
                                }
                              },
                              reverse: true,
                              child: ListView.builder(
                                reverse: true,
                                padding: EdgeInsets.zero,
                                controller: chatProvider.scrollController,
                                itemCount: groupedMessages.length,
                                itemBuilder: (context, groupIndex) {
                                  final group = groupedMessages[groupIndex];
                                  final dateTitle = group.key;
                                  final messages = group.value;

                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                          vertical: AppSize.h8,
                                        ),
                                        child: Center(
                                          child: Text(
                                            dateTitle,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                      ...List.generate(messages.length, (
                                        msgIndex,
                                      ) {
                                        final msg = messages[msgIndex];
                                        final isNextMsgSameSender =
                                            msgIndex + 1 < messages.length &&
                                            messages[msgIndex + 1].sender ==
                                                msg.sender;
                                        return ChatBubble(
                                          message: msg,
                                          isUser:
                                              msg.sender?.id ==
                                              chatProvider.userId,
                                          isNextMsgSameSender:
                                              isNextMsgSameSender,
                                        );
                                      }).reversed,
                                    ],
                                  );
                                },
                              ),
                            ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                      child:
                          ((chatParams.customerChatRoomParameter?.isActive ??
                                  false) ||
                              (chatProvider.chatRoom?.isActive ?? false))
                          ? AppTextFormField(
                              controller: chatProvider.chatController,
                              hintText: context.l10n.writeUrMessageHere,
                              keyboardType: TextInputType.multiline,
                              maxTextLength: 300,
                              maxLine: 4,
                              suffixIcon: GestureDetector(
                                onTap: () {
                                  if ((chatParams
                                              .customerChatRoomParameter
                                              ?.isActive ??
                                          false) ||
                                      (chatProvider.chatRoom?.isActive ??
                                          false)) {
                                    chatProvider.sendMessage();

                                    /// this function is used to add or update
                                    /// chat room data manually coz first time
                                    /// it will be null and update if isActive
                                    /// status is changed
                                    if ((chatProvider.chatRoom?.id
                                                    .toString()
                                                    .isNotEmptyAndNotNull ??
                                                false) &&
                                            chatParams
                                                    .customerChatRoomParameter ==
                                                null ||
                                        (chatParams
                                                .customerChatRoomParameter
                                                ?.isActive !=
                                            chatProvider.chatRoom?.isActive)) {
                                      chatParams.addDriverChatRoomData?.call(
                                        chatProvider.chatRoom?.id ?? -1,
                                        chatProvider.chatRoom?.isActive ??
                                            false,
                                      );
                                    }
                                  }
                                },
                                child: AppAssets.iconsSend.image(
                                  height: AppSize.h20,
                                  width: AppSize.h20,
                                ),
                              ),
                            )
                          : !chatProvider.isLoading
                          ? Text(context.l10n.chatInactive)
                          : const SizedBox.shrink(),
                    ),
                    Gap(AppSize.h10),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
