// ignore_for_file: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_detail_page/provider/customer_detail_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/mobile_text_filed.dart';

class CustomerDetailScreen extends StatelessWidget {
  const CustomerDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CustomerDetailProvider(),
      child: Form(
        child: Builder(
          builder: (context) {
            final customerDetailProvider = context
                .read<CustomerDetailProvider>();
            return Scaffold(
              backgroundColor: AppColors.pageBGColor,
              appBar: CustomAppBar(title: context.l10n.customerDetail),
              body: ValueListenableBuilder(
                valueListenable: customerDetailProvider.isShowLoader,
                builder: (context, isShowLoader, _) {
                  return AppLoader(
                    isShowLoader: isShowLoader,
                    child: ListView(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSize.appPadding,
                      ),
                      children: [
                        Selector<CustomerDetailProvider, (String, int)>(
                          selector: (p0, customerDetailProvider) => (
                            customerDetailProvider.countryCode,
                            customerDetailProvider.mobileNumberLength,
                          ),
                          builder: (context, codeLen, child) {
                            return MobileTextFiled(
                              controller:
                                  customerDetailProvider.mobileNumberController,
                              maxTextLength: codeLen.$2,
                              countryCode: codeLen.$1,
                              onSelect: customerDetailProvider.changeCountry,
                            );
                          },
                        ),
                        Text(
                          context.l10n.noteForMobile,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: AppColors.errorColor,
                            fontSize: AppSize.sp10,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              bottomNavigationBar: AppButton(
                text: context.l10n.save,
                onPressed: () {
                  if (Form.of(context).validate()) {
                    customerDetailProvider.addUpdateUserDetailData(context);
                  }
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
