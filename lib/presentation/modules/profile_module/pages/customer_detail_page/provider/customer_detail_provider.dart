import 'package:country_picker/country_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class CustomerDetailProvider extends ChangeNotifier {
  CustomerDetailProvider() {
    getUserDetailData();
  }

  String countryCode = kDebugMode ? '+91' : '+52';
  int mobileNumberLength = kDebugMode ? 10 : 11;
  final mobileNumberController = TextEditingController();

  bool _isClosed = false;
  void notify() {
    if (_isClosed) return;
    notifyListeners();
  }

  final isShowLoader = ValueNotifier<bool>(false);

  void changeCountry(Country country) {
    countryCode = '+${country.phoneCode}';
    mobileNumberLength = country.example.length;
    'mobileNumberLength => $mobileNumberLength'.logE;
    notify();
  }

  CancelToken? getDetailCancelToken;

  Future<void> getUserDetailData() async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      getDetailCancelToken?.cancel();
      getDetailCancelToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.getCustomerDetail}${Injector.instance<AppDB>().userModel?.user?.userDetailData?.id}/',
        cancelToken: getDetailCancelToken,
      );
      final res = await Injector.instance<AccountRepository>()
          .getCustomerDetail(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          if (_isClosed || (getDetailCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          mobileNumberController.text = data.bookingNumber ?? '';
          if (mobileNumberController.text.isNotEmpty) {
            mobileNumberLength = mobileNumberController.text.length;
          }
          if (data.bookingCountryCode.isNotEmptyAndNotNull) {
            countryCode = data.bookingCountryCode ?? '';
          }

          // Injector.instance<AppDB>().userModel = data;
          notify();
        },
        error: (exception) async {
          if (_isClosed || (getDetailCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getDetailCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'get user detail error: $e'.logE;
    }
  }

  CancelToken? updateDetailCancelToken;
  Future<void> addUpdateUserDetailData(BuildContext context) async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      updateDetailCancelToken?.cancel();
      updateDetailCancelToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.getCustomerDetail}${Injector.instance<AppDB>().userModel?.user?.userDetailData?.id}/',
        cancelToken: updateDetailCancelToken,
        data: {
          ApiKeys.bookingNumber: mobileNumberController.text,
          ApiKeys.bookingNumberCode: countryCode,
        },
      );
      final res = await Injector.instance<AccountRepository>()
          .addUpdateCustomerDetail(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          if (_isClosed || (updateDetailCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          context.l10n.customerDetailSavedSuccess.showSuccessAlert();
          AppNavigationService.pop(context);
          // Injector.instance<AppDB>().userModel = data;
          notify();
        },
        error: (exception) async {
          if (_isClosed || (updateDetailCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (updateDetailCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'update user detail error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    isShowLoader.value = false;
    mobileNumberController.clear();

    super.dispose();
  }
}
