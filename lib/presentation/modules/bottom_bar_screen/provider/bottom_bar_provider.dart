import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class BottomBarProvider extends ChangeNotifier {
  BottomBarProvider() {
    Future.delayed(Duration.zero, getUnreadNotificationCount);
  }

  bool __isClosed = false;

  int unreadNotificationCount = 0;

  void resetUnreadNotificationCount() {
    unreadNotificationCount = 0;
    notifyListeners();
    notificationMarkRead();
  }

  CancelToken? notificationCancelToken;
  Future<void> getUnreadNotificationCount() async {
    if (__isClosed) return;
    notificationCancelToken = CancelToken();
    notificationCancelToken?.cancel();
    try {
      final response = await Injector.instance<AccountRepository>()
          .getUnreadNotificationCount(
        const ApiRequest(
          path: EndPoints.unreadNotificationCount,
        ),
      );
      response.when(
        success: (data) {
          if (__isClosed && (notificationCancelToken?.isCancelled ?? true)) {
            return;
          }
          unreadNotificationCount =
              int.tryParse(data['unread'].toString()) ?? 0;
          notifyListeners();
        },
        error: (e) {
          if (__isClosed && (notificationCancelToken?.isCancelled ?? true)) {
            return;
          }
          // 'Exception: ==== >> $e'.showErrorAlert();
        },
      );
    } catch (e) {
      if (__isClosed && (notificationCancelToken?.isCancelled ?? true)) return;
    }
  }

  CancelToken? notificationMarkCancelToken;
  Future<void> notificationMarkRead() async {
    if (__isClosed) return;
    notificationMarkCancelToken = CancelToken();
    notificationMarkCancelToken?.cancel();
    try {
      await Injector.instance<AccountRepository>().notificationMarkRead(
        const ApiRequest(
          path: EndPoints.notificationMarkRead,
          // cancelToken: notificationMarkCancelToken,
        ),
      );
    } catch (e) {
      if (__isClosed || (notificationMarkCancelToken?.isCancelled ?? true)) {
        return;
      }
      'Exception: $e'.logE;
    }
  }

  @override
  void dispose() {
    __isClosed = true;
    // notificationCancelToken?.cancel();
    notificationMarkCancelToken?.cancel();
    super.dispose();
  }
}
